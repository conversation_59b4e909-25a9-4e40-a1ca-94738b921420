package com.tmb.oneapp.commonpaymentexp.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.AmountDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CompleteScreenDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PhraseDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.Schedule;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(MockitoExtension.class)
class AsymmetricKeyUtilityTest {
    private static final String PRIVATE_KEY_STR = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCtmgUDIsV4pXrcFXK5/kgXFDoh2PCy02qngo2eU6oWHKubVIlVdscMim8SZPlmFHMCLm/G6BFykXFoNpKnJKxaFtkW4ah0WBXAW6x9w8wh61aIK3lugULUz+Dy//dQKyYhp0KKqBmKjfKYWp/vbRAFCNFH3gtNIx0rXnbP6dawr/mPfVlVDgxhB2MuDYs7QwK7PsmLfRcYafJtKnG1jzgFNMUz1Ko3A+zeEWXEPPi0h8L9ObuuINGNbX6KPCFkxVea4tGFKoETBbZMTCSItMGET7D4OUdXZ8+Lb2RFf4fZsHYyZ7gkPmkPpVQbXYeryseApvcDQyFbG28vW8hceAWRAgMBAAECggEAA7++ppDJKyMghWedkloYWQ5x3opBt6YfxWqCpjrDT3ZWCstnRD+46h+EaSUu5Gv1O3ftdQiAKMIh7PC393RJmCmqWlEMPMFfbHruVv1aYRi+eSYkzLFtuoybHIeEYg665GFcazclhtvGGerKTZ1EcJj+QATFDk9MUXcl4YHFkticDeCqgRuD8T5Oti3Mwnb5LnZ+GZhvPwgB2tQWkLKstq/tJbz1LH0oGn9j+A6QK/prc9/eMMGo4JL80v0EsI/XhE/F6EBSO2ER2y9HCRYDtwqsF3/97xxQmsJPFN5AWpaRlrt2UpmdSxirbYtAkbvncNCo5dblrxewq23hAB6d2QKBgQDvdhqUr6glGW0yE+RJKccFuw6cXBVJL9/cV8pzVKPYeeBQNxTV1Mlc6ojTJvQaGZ2BaeVPnl/X//ui9Blj2dPTGKzZKWfEuIcRuJjjvdxFYTGFS4NH9nsM5XWk2fEOhGMYtgLGfUtT8+i+MO/F5BUn6mD9HQ269/FZc/iv00LXDQKBgQC5l3SttxpJUwZibkS6EhyHgysZuYBcP9KV7FyTf7dU85mn0jSRUqrj8TzFk/IGENUfrfhAdwcGEDWkmmo10E4rG54G+BvJRK4iuy4oXK4x5Mnttcamh5s3e9P4+v0dT+0M8JCGtFP2kuOmwNRwuuhv8Iz4KbbMK6zLv5fDSY2HlQKBgQCzNalI83kcYr4VB0wG8UwwXsDdhhCsGDcqOU2bKJIClZupjL98FGaqsh+6HuH6kocM7kqeXoGOLtaNKq/Byai8yC+7ykANuqfjPEq3zkSGa+navxu+BJosmaGEwVKlsbBUMws6r1RrvRPHGuqnKD4pBECXzWGUNrEaaC+KrsnMFQKBgGYA7Ewd+wCZ9URcPsHV+oAwg5vb7MTG/0MPacJXy7eRO76VNfc5bFtj13bwXXEgB0MmJu+ne+DFP/Z8OCvjlVL0FuLVss/oQ50rK69bYRJjIOjZo5kbpx/TmymZKUzXBP67qXOf4RYAncak7R4YXHBJ0fEDXEjTKGHDp/HZl0QRAoGBAM/ppnJ6daI4InNt2iOx2wNk7rWRazut9r89XbbhqekQ7T+0ABIKNGiBSEzJG6qNrl5pPcd0x1vzR47/Tc5yHOysZhtIla8r1kiiFRW6AN/lw8vJDHK1cYdNc6eq+vqpxIwcrO46mL9vhyiaN7GTZSVOj4tnIViYsJ2I7Zb72oRi";
    private static final String PUBLIC_KEY_STR = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArZoFAyLFeKV63BVyuf5IFxQ6IdjwstNqp4KNnlOqFhyrm1SJVXbHDIpvEmT5ZhRzAi5vxugRcpFxaDaSpySsWhbZFuGodFgVwFusfcPMIetWiCt5boFC1M/g8v/3UCsmIadCiqgZio3ymFqf720QBQjRR94LTSMdK152z+nWsK/5j31ZVQ4MYQdjLg2LO0MCuz7Ji30XGGnybSpxtY84BTTFM9SqNwPs3hFlxDz4tIfC/Tm7riDRjW1+ijwhZMVXmuLRhSqBEwW2TEwkiLTBhE+w+DlHV2fPi29kRX+H2bB2Mme4JD5pD6VUG12Hq8rHgKb3A0MhWxtvL1vIXHgFkQIDAQAB";

    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
    }

    @Test
    void testEncryptDecrypt_shouldHandleLargePayload() throws Exception {
        PaymentInformation originalDto = createFullTestPaymentInformation();
        String originalJson = objectMapper.writeValueAsString(originalDto);

        String encryptedJson = AsymmetricKeyUtility.encrypt(originalJson, PUBLIC_KEY_STR);
        String decryptedJson = AsymmetricKeyUtility.decrypt(encryptedJson, PRIVATE_KEY_STR);
        PaymentInformation decryptedDto = objectMapper.readValue(decryptedJson, PaymentInformation.class);

        assertNotNull(encryptedJson);
        assertNotNull(decryptedJson);
        assertEquals(originalDto, decryptedDto);
    }

    private PaymentInformation createFullTestPaymentInformation() {
        AmountDetail amountDetail = new AmountDetail()
                .setAmountValue(new BigDecimal("12345.67"))
                .setAmountLabelEn("Total Amount")
                .setAmountLabelTh("ยอดรวม")
                .setPaymentAmount(new BigDecimal("12345.67"));

        PhraseDetail phraseDetail1 = new PhraseDetail()
                .setLabelEn("Customer Name")
                .setLabelTh("ชื่อลูกค้า")
                .setValueEn("Mr. John Doe")
                .setValueTh("นายจอห์น โด");

        PhraseDetail phraseDetail2 = new PhraseDetail()
                .setLabelEn("Invoice No.")
                .setLabelTh("เลขที่ใบแจ้งหนี้")
                .setValueEn("INV-2025-001")
                .setValueTh("INV-2025-001");

        ProductDetail productDetail = new ProductDetail()
                .setProductNameEn("Electricity Bill")
                .setProductNameTh("ค่าไฟฟ้า")
                .setProductRef1("REF-1111111111")
                .setProductRef2("REF-2222222222")
                .setProductAttributeList(java.util.Arrays.asList(phraseDetail1, phraseDetail2));

        CompleteScreenDetail completeScreenDetail = new CompleteScreenDetail()
                .setRemarkEn("Payment successful. Thank you for your payment.")
                .setRemarkTh("ชำระเงินสำเร็จ ขอบคุณที่ใช้บริการ")
                .setFooterEn("This is a footer message.")
                .setFooterTh("นี่คือข้อความส่วนท้าย");

        Schedule schedule = new Schedule()
                .setScheduleName("Monthly Bill Payment")
                .setFrequencyType("MONTHLY")
                .setStartDate("2025-07-01");

        return new PaymentInformation()
                .setEntryId("entry-id-54321-abcde")
                .setTransactionType("BILLPAYMENT_SERVICE")
                .setCompCode("MEA")
                .setRequireAddressFlag(true)
                .setProductDetail(productDetail)
                .setAmountDetail(amountDetail)
                .setCompleteScreenDetail(completeScreenDetail)
                .setSchedule(schedule);
    }
}
