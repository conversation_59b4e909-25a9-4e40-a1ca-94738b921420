package com.tmb.oneapp.commonpaymentexp.utils;

import com.nimbusds.jose.Payload;
import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jose.jwk.RSAKey;
import com.nimbusds.jose.jwk.gen.RSAKeyGenerator;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.AmountDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.service.JwkSetProvider;
import com.tmb.oneapp.commonpaymentexp.config.JwkConfigProperties;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nimbusds.jose.jwk.JWK;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.Collections;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

@ExtendWith(MockitoExtension.class)
class JoseUtilsTest {

    // For legacy string-based methods
    private static String publicKeyStr;
    private static String privateKeyStr;
    private static String anotherPublicKeyStr;

    // For new JWK-based methods
    private static RSAKey rsaKey;
    private static RSAKey anotherRsaKey;
    private static JWKSet jwkSet;

    @BeforeAll
    static void setUp() throws Exception {
        // Setup for legacy methods
        KeyPair keyPair = generateRsaKeyPair();
        publicKeyStr = Base64.getEncoder().encodeToString(keyPair.getPublic().getEncoded());
        privateKeyStr = Base64.getEncoder().encodeToString(keyPair.getPrivate().getEncoded());
        anotherPublicKeyStr = Base64.getEncoder().encodeToString(generateRsaKeyPair().getPublic().getEncoded());

        // Setup for new JWK-based methods
        rsaKey = new RSAKeyGenerator(2048).keyID("test-kid-1").generate();
        anotherRsaKey = new RSAKeyGenerator(2048).keyID("test-kid-2").generate();
        jwkSet = new JWKSet(rsaKey);
    }

    private static KeyPair generateRsaKeyPair() throws NoSuchAlgorithmException {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
        keyPairGenerator.initialize(2048);
        return keyPairGenerator.generateKeyPair();
    }

    @Nested
    @DisplayName("JWK-based Methods")
    class JwkBasedTests {
        @Test
        @DisplayName("Should encrypt and decrypt successfully with valid JWKs")
        void testEncryptAndDecrypt_Success() throws TMBCommonException {
            String originalText = "This is a secret message for testing JWE!";
            RSAKey rsaPublicKey = rsaKey.toPublicJWK();
            String encryptedText = JoseUtils.encrypt(originalText, rsaPublicKey);
            String decryptedText = JoseUtils.decrypt(encryptedText, jwkSet);
            assertEquals(originalText, decryptedText);
        }

        @Test
        @DisplayName("Should throw exception when decrypting with wrong JWKSet")
        void testDecrypt_withWrongKey_shouldThrowException() throws TMBCommonException {
            String encryptedText = JoseUtils.encrypt("test", rsaKey.toPublicJWK());
            JWKSet wrongJwkSet = new JWKSet(anotherRsaKey);
            assertThrows(TMBCommonException.class, () -> JoseUtils.decrypt(encryptedText, wrongJwkSet));
        }

        @Test
        @DisplayName("Should generate and verify JWS successfully with valid JWKs")
        void testGenerateAndVerifyJws_Success() throws TMBCommonException {
            Map<String, Object> claims = Collections.singletonMap("sub", "1234567890");
            String jwsString = JoseUtils.generateJws(rsaKey, claims);
            Payload verifiedPayload = JoseUtils.verifyJws(jwsString, rsaKey.toPublicJWK());
            assertEquals("{\"sub\":\"1234567890\"}", verifiedPayload.toString());
        }

        @Test
        @DisplayName("Should throw exception when verifying JWS with wrong public JWK")
        void testVerifyJws_withWrongKey_shouldThrowException() throws TMBCommonException {
            String jwsString = JoseUtils.generateJws(rsaKey, Collections.singletonMap("sub", "user-id"));
            assertThrows(TMBCommonException.class, () -> JoseUtils.verifyJws(jwsString, anotherRsaKey.toPublicJWK()));
        }
    }

    @Nested
    @DisplayName("String-based (Legacy) Methods")
    class StringBasedTests {
        @Test
        @DisplayName("Should encrypt and decrypt successfully with valid key strings")
        void testEncryptAndDecrypt_Success() throws TMBCommonException {
            String originalText = "Legacy test message";
            String encryptedText = JoseUtils.encrypt(originalText, publicKeyStr);
            String decryptedText = JoseUtils.decrypt(encryptedText, privateKeyStr);
            assertEquals(originalText, decryptedText);
        }

        @Test
        @DisplayName("Should generate and verify JWS successfully with valid key strings")
        void testGenerateAndVerifyJws_Success() throws TMBCommonException {
            Map<String, Object> claims = Collections.singletonMap("sub", "legacy-user");
            String jwsString = JoseUtils.generateJws(privateKeyStr, claims);
            Payload verifiedPayload = JoseUtils.verifyJws(jwsString, publicKeyStr);
            assertEquals("{\"sub\":\"legacy-user\"}", verifiedPayload.toString());
        }

        @Test
        @DisplayName("Should throw exception when verifying JWS with wrong public key string")
        void testVerifyJws_withWrongKey_shouldThrowException() throws TMBCommonException {
            String jwsString = JoseUtils.generateJws(privateKeyStr, Collections.singletonMap("sub", "user"));
            assertThrows(TMBCommonException.class, () -> JoseUtils.verifyJws(jwsString, anotherPublicKeyStr));
        }

        @Test
        @DisplayName("Should throw exception when using invalid key string format")
        void testMethods_withInvalidKeyFormat_shouldThrowException() {
            String invalidKey = "this-is-not-a-valid-base64-key";
            assertThrows(TMBCommonException.class, () -> JoseUtils.encrypt("test", invalidKey));
            assertThrows(TMBCommonException.class, () -> JoseUtils.generateJws(invalidKey, Collections.emptyMap()));
        }
    }

    @Test
    void testGeneratePayloadEncrypt() {
        String partnerName = "shopee";
        PaymentInformation paymentInformation = new PaymentInformation();
        paymentInformation.setEntryId("123456");
        paymentInformation.setTransactionType("bill_pay");
        paymentInformation.setCompCode("123456");
        paymentInformation.setFundCode("123456");
        paymentInformation.setRequireAddressFlag(true);
        paymentInformation.setProductDetail(new ProductDetail());
        paymentInformation.setAmountDetail(new AmountDetail());
    }
}
