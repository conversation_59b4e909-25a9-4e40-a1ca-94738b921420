package com.tmb.oneapp.commonpaymentexp.controller.security;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitializationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitializationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.security.EncryptedPayloadRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.security.PublicKeyResponse;
import com.tmb.oneapp.commonpaymentexp.service.InitializationCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.PartnerIntegrationService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PartnerIntegrationControllerTest {

    @Mock
    private PartnerIntegrationService partnerIntegrationService;

    @Mock
    private InitializationCommonPaymentService initializationCommonPaymentService;

    @InjectMocks
    private PartnerIntegrationController partnerIntegrationController;

    @Test
    void testGetPublicKeySuccess() throws TMBCommonException {
        // Given
        String partnerName = "test-partner";
        Map<String, Object> jwkSetMap = Map.of("kty", "RSA", "e", "AQAB");
        PublicKeyResponse publicKeyResponse = new PublicKeyResponse(jwkSetMap);

        when(partnerIntegrationService.getPublicKey(partnerName)).thenReturn(publicKeyResponse);

        // When
        ResponseEntity<TmbServiceResponse<PublicKeyResponse>> responseEntity = partnerIntegrationController.getPublicKey(partnerName);

        // Then
        assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
        TmbServiceResponse<PublicKeyResponse> responseBody = responseEntity.getBody();
        assertNotNull(responseBody);
        assertEquals(ResponseCode.SUCCESS_V2.getCode(), responseBody.getStatus().getCode());
        assertEquals(ResponseCode.SUCCESS_V2.getMessage(), responseBody.getStatus().getMessage());
        assertNotNull(responseBody.getData());
        assertEquals(jwkSetMap, responseBody.getData().getJwkSet());
    }

    @Test
    void testGetPublicKeyPartnerNotFound() throws TMBCommonException {
        // Given
        String partnerName = "unknown-partner";
        TMBCommonException givenException = new TMBCommonException(
                ResponseCode.FAILED_V2.getCode(),
                "JWKSet not found for client: " + partnerName,
                ResponseCode.FAILED_V2.getService(),
                HttpStatus.BAD_REQUEST,
                null
        );

        when(partnerIntegrationService.getPublicKey(partnerName)).thenThrow(givenException);

        // When & Then
        TMBCommonException thrownException = assertThrows(TMBCommonException.class, () -> {
            partnerIntegrationController.getPublicKey(partnerName);
        });

        // Assert that the thrown exception is the one we expect
        assertEquals(givenException.getErrorCode(), thrownException.getErrorCode());
        assertEquals(givenException.getErrorMessage(), thrownException.getErrorMessage());
    }

    @Test
    void testInitialEncryptedPaymentSuccess() throws TMBCommonException {
        // Given
        String partnerName = "test-partner";
        String encryptedPayloadStr = "ey...encrypted...";
        EncryptedPayloadRequest encryptedRequest = new EncryptedPayloadRequest();
        encryptedRequest.setPayload(encryptedPayloadStr);

        InitializationCommonPaymentRequest decryptedRequest = new InitializationCommonPaymentRequest();
        InitializationCommonPaymentResponse mockResponseData = new InitializationCommonPaymentResponse();
        HttpHeaders headers = new HttpHeaders();

        when(partnerIntegrationService.decryptInitialPayload(encryptedPayloadStr, partnerName)).thenReturn(decryptedRequest);
        when(initializationCommonPaymentService.initialCommonPayment(decryptedRequest, headers)).thenReturn(mockResponseData);

        // When
        ResponseEntity<TmbServiceResponse<InitializationCommonPaymentResponse>> responseEntity =
                partnerIntegrationController.initialEncryptedPayment(partnerName, encryptedRequest, headers);

        // Then
        assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
        TmbServiceResponse<InitializationCommonPaymentResponse> responseBody = responseEntity.getBody();
        assertNotNull(responseBody);
        assertEquals(ResponseCode.SUCCESS_V2.getCode(), responseBody.getStatus().getCode());
        assertEquals(mockResponseData, responseBody.getData());
    }

    @Test
    void testInitialEncryptedPaymentDecryptionFailed() throws TMBCommonException {
        // Given
        String partnerName = "test-partner";
        String encryptedPayloadStr = "ey...invalid-encrypted...";
        EncryptedPayloadRequest encryptedRequest = new EncryptedPayloadRequest();
        encryptedRequest.setPayload(encryptedPayloadStr);
        HttpHeaders headers = new HttpHeaders();
        TMBCommonException givenException = new TMBCommonException(
                ResponseCode.FAILED_V2.getCode(),
                "Decryption failed",
                ResponseCode.FAILED_V2.getService(),
                HttpStatus.BAD_REQUEST,
                null
        );

        when(partnerIntegrationService.decryptInitialPayload(encryptedPayloadStr, partnerName)).thenThrow(givenException);

        // When & Then
        TMBCommonException thrownException = assertThrows(TMBCommonException.class, () -> {
            partnerIntegrationController.initialEncryptedPayment(partnerName, encryptedRequest, headers);
        });

        assertEquals(givenException.getErrorCode(), thrownException.getErrorCode());
    }
}
