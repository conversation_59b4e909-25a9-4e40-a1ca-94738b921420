package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentConfig;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.PaymentStatus;
import com.tmb.oneapp.commonpaymentexp.utils.JoseUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CallbackConfirmServiceTest {

    @Mock
    private RestTemplate restTemplate;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private CallbackConfirmService callbackConfirmService;

    private MockedStatic<JoseUtils> joseUtilsMockedStatic;

    private static final String CALLBACK_URL = "https://api.example.com/callback";
    private static final String API_KEY = "some-valid-api-key";
    private static final String MOCKED_JWS = "mocked.jws.token";

    @BeforeEach
    void setUp() {
        joseUtilsMockedStatic = Mockito.mockStatic(JoseUtils.class);
    }

    @AfterEach
    void tearDown() {
        joseUtilsMockedStatic.close();
    }

    @Test
    void testCallbackWhenSuccessThenNotThrowException() throws TMBCommonException {
        CommonPaymentConfig config = new CommonPaymentConfig();
        config.setCallbackUrl(CALLBACK_URL);
        config.setCallbackApiKey(API_KEY);
        PaymentStatus status = PaymentStatus.builder().status("success").build();
        Map<String, Object> claims = new HashMap<>();
        ResponseEntity<TmbServiceResponse> expectedResponse = new ResponseEntity<>(new TmbServiceResponse(), HttpStatus.OK);
        ArgumentCaptor<HttpEntity<PaymentStatus>> requestCaptor = ArgumentCaptor.forClass(HttpEntity.class);

        when(objectMapper.convertValue(any(PaymentStatus.class), any(TypeReference.class))).thenReturn(claims);
        joseUtilsMockedStatic.when(() -> JoseUtils.generateJws(API_KEY, claims)).thenReturn(MOCKED_JWS);
        when(restTemplate.exchange(eq(CALLBACK_URL), eq(HttpMethod.POST), requestCaptor.capture(), eq(TmbServiceResponse.class))).thenReturn(expectedResponse);

        callbackConfirmService.callback(config, status);

        verify(restTemplate).exchange(eq(CALLBACK_URL), eq(HttpMethod.POST), any(HttpEntity.class), eq(TmbServiceResponse.class));
        HttpHeaders actualHeaders = requestCaptor.getValue().getHeaders();
        assertNotNull(actualHeaders.getFirst("secret"));
        assert(actualHeaders.getFirst("secret").equals(MOCKED_JWS));
    }

    @Test
    void testCallbackWhenJwsGenerationFailsThenNotThrowsExceptionAndNotCallback() throws TMBCommonException {
        CommonPaymentConfig config = new CommonPaymentConfig();
        config.setCallbackUrl(CALLBACK_URL);
        config.setCallbackApiKey("invalid-key");
        PaymentStatus status = PaymentStatus.builder().status("success").build();
        Map<String, Object> claims = new HashMap<>();

        when(objectMapper.convertValue(any(PaymentStatus.class), any(TypeReference.class))).thenReturn(claims);
        joseUtilsMockedStatic.when(() -> JoseUtils.generateJws("invalid-key", claims)).thenThrow(new TMBCommonException("JWS Generation Failed"));

        assertDoesNotThrow(() -> callbackConfirmService.callback(config, status), "Should handle JWS generation failure gracefully");
        verify(restTemplate, never()).exchange(anyString(), any(HttpMethod.class), any(HttpEntity.class), eq(TmbServiceResponse.class));
    }

    @Test
    void testCallbackWhenRestClientExceptionThenDoNotThrowsException() throws TMBCommonException {
        CommonPaymentConfig config = new CommonPaymentConfig();
        config.setCallbackUrl(CALLBACK_URL);
        config.setCallbackApiKey(API_KEY);
        PaymentStatus status = PaymentStatus.builder().status("success").build();
        Map<String, Object> claims = new HashMap<>();

        when(objectMapper.convertValue(any(PaymentStatus.class), any(TypeReference.class))).thenReturn(claims);
        joseUtilsMockedStatic.when(() -> JoseUtils.generateJws(API_KEY, claims)).thenReturn(MOCKED_JWS);
        when(restTemplate.exchange(anyString(), any(HttpMethod.class), any(HttpEntity.class), eq(TmbServiceResponse.class))).thenThrow(new RestClientException("Connection error"));

        assertDoesNotThrow(() -> callbackConfirmService.callback(config, status));
    }

    @Test
    void testCallbackWhenConfigIsNullThenDoNothing() {
        PaymentStatus status = PaymentStatus.builder().build();

        assertDoesNotThrow(() -> callbackConfirmService.callback(null, status));
        verify(restTemplate, never()).exchange(anyString(), any(HttpMethod.class), any(HttpEntity.class), eq(TmbServiceResponse.class));
    }

    @Test
    void testCallbackWhenStatusIsNullThenDoNothing() {
        CommonPaymentConfig config = new CommonPaymentConfig();

        assertDoesNotThrow(() -> callbackConfirmService.callback(config, null));
        verify(restTemplate, never()).exchange(anyString(), any(HttpMethod.class), any(HttpEntity.class), eq(TmbServiceResponse.class));
    }

    @Test
    void testCallbackWhenNonSuccessResponseThenDoNotThrowsException() throws TMBCommonException {
        CommonPaymentConfig config = new CommonPaymentConfig();
        config.setCallbackUrl(CALLBACK_URL);
        config.setCallbackApiKey(API_KEY);
        PaymentStatus status = PaymentStatus.builder().status("success").build();
        Map<String, Object> claims = new HashMap<>();
        ResponseEntity<TmbServiceResponse> failedResponse = new ResponseEntity<>(new TmbServiceResponse(), HttpStatus.INTERNAL_SERVER_ERROR);

        when(objectMapper.convertValue(any(PaymentStatus.class), any(TypeReference.class))).thenReturn(claims);
        joseUtilsMockedStatic.when(() -> JoseUtils.generateJws(API_KEY, claims)).thenReturn(MOCKED_JWS);
        when(restTemplate.exchange(anyString(), any(HttpMethod.class), any(HttpEntity.class), eq(TmbServiceResponse.class))).thenReturn(failedResponse);

        assertDoesNotThrow(() -> callbackConfirmService.callback(config, status));
    }
}
