package com.tmb.oneapp.commonpaymentexp.service;

import com.nimbusds.jose.jwk.JWKSet;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitializationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.security.PublicKeyResponse;
import com.tmb.oneapp.commonpaymentexp.utils.JoseUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

import java.text.ParseException;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PartnerIntegrationServiceTest {

    @Mock
    private JwkSetProvider jwkSetProvider;

    @InjectMocks
    private PartnerIntegrationService partnerIntegrationService;

    private JWKSet jwkSet;
    private String partnerName = "test-partner";

    @BeforeEach
    void setUp() throws ParseException {
        String jwkSetJsonString = "{\"keys\":[{\"p\":\"...\",\"kty\":\"RSA\",\"q\":\"...\",\"d\":\"...\",\"e\":\"AQAB\",\"use\":\"sig\",\"kid\":\"test-key\",\"qi\":\"...\",\"dp\":\"...\",\"alg\":\"RS256\",\"dq\":\"...\",\"n\":\"...\"}]}";
        jwkSet = JWKSet.parse(jwkSetJsonString);
    }

    @Test
    void testGetPublicKeySuccess() throws TMBCommonException {
        // Given
        String partnerName = "test-partner";
        when(jwkSetProvider.getJwkSet(partnerName)).thenReturn(jwkSet);

        // When
        PublicKeyResponse response = partnerIntegrationService.getPublicKey(partnerName);

        // Then
        assertNotNull(response);
        Map<String, Object> expectedMap = jwkSet.toPublicJWKSet().toJSONObject();
        assertEquals(expectedMap, response.getJwkSet());
    }

    @Test
    void testGetPublicKeyNotFound() throws TMBCommonException {
        // Given
        String partnerName = "unknown-partner";
        TMBCommonException givenException = new TMBCommonException(
                ResponseCode.FAILED_V2.getCode(),
                "JWKSet not found",
                ResponseCode.FAILED_V2.getService(),
                HttpStatus.BAD_REQUEST,
                null
        );

        when(jwkSetProvider.getJwkSet(partnerName)).thenThrow(givenException);

        // When & Then
        TMBCommonException thrownException = assertThrows(TMBCommonException.class, () -> partnerIntegrationService.getPublicKey(partnerName));

        assertEquals(givenException.getErrorCode(), thrownException.getErrorCode());
        assertEquals(givenException.getErrorMessage(), thrownException.getErrorMessage());
    }
}
