package com.tmb.oneapp.commonpaymentexp.service;

import com.nimbusds.jose.jwk.JWK;
import com.nimbusds.jose.jwk.JWKSet;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.commonpaymentexp.config.JwkConfigProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class JwkSetProviderTest {

    @Mock
    private JwkConfigProperties jwkConfigProperties;

    @Mock
    private JwkConfigProperties.Sets sets;

    @InjectMocks
    private JwkSetProvider jwkSetProvider;

    private final String validJwkSetJson = "{\"keys\":[{\"kty\":\"RSA\",\"e\":\"AQAB\",\"kid\":\"test-key-1\",\"alg\":\"RS256\",\"n\":\"...\"}]}";
    private final String clientId = "test-client";
    private final String keyId = "test-key-1";

    @BeforeEach
    void setUp() {
        // We need to manually call the @PostConstruct method in a unit test
        // as Spring context is not involved.
    }

    @Test
    void testInit_Success() {
        // Given
        Map<String, String> clientMap = Collections.singletonMap(clientId, validJwkSetJson);
        when(jwkConfigProperties.getSets()).thenReturn(sets);
        when(sets.getClient()).thenReturn(clientMap);

        // When
        // Manually trigger @PostConstruct logic
        ReflectionTestUtils.invokeMethod(jwkSetProvider, "init");

        // Then
        assertDoesNotThrow(() -> {
            JWKSet jwkSet = jwkSetProvider.getJwkSet(clientId);
            assertNotNull(jwkSet);
            assertEquals(1, jwkSet.size());
        });
    }

    @Test
    void testInit_InvalidJson_shouldThrowException() {
        // Given
        Map<String, String> clientMap = Collections.singletonMap(clientId, "{\"invalid-json\"");
        when(jwkConfigProperties.getSets()).thenReturn(sets);
        when(sets.getClient()).thenReturn(clientMap);

        // When & Then
        assertThrows(IllegalStateException.class, () -> {
            ReflectionTestUtils.invokeMethod(jwkSetProvider, "init");
        });
    }

    @Test
    void testInit_NullClientMap_shouldNotThrowException() {
        // Given
        when(jwkConfigProperties.getSets()).thenReturn(sets);
        when(sets.getClient()).thenReturn(null);

        // When & Then
        assertDoesNotThrow(() -> {
            ReflectionTestUtils.invokeMethod(jwkSetProvider, "init");
        });
    }

    @Test
    void testGetJwkSet_Success() {
        // Setup the provider first
        testInit_Success();

        // When
        JWKSet jwkSet = assertDoesNotThrow(() -> jwkSetProvider.getJwkSet(clientId));

        // Then
        assertNotNull(jwkSet);
        assertEquals(1, jwkSet.getKeys().size());
    }

    @Test
    void testGetJwkSet_NotFound_shouldThrowException() {
        // Setup the provider first
        testInit_Success();

        // When & Then
        assertThrows(TMBCommonException.class, () -> {
            jwkSetProvider.getJwkSet("unknown-client");
        });
    }

    @Test
    void testGetKeyById_Success() {
        // Setup the provider first
        testInit_Success();

        // When
        JWK jwk = assertDoesNotThrow(() -> jwkSetProvider.getKeyById(clientId, keyId));

        // Then
        assertNotNull(jwk);
        assertEquals(keyId, jwk.getKeyID());
    }

    @Test
    void testGetKeyById_KeyNotFound_shouldThrowException() {
        // Setup the provider first
        testInit_Success();

        // When & Then
        assertThrows(TMBCommonException.class, () -> {
            jwkSetProvider.getKeyById(clientId, "unknown-key");
        });
    }
}
