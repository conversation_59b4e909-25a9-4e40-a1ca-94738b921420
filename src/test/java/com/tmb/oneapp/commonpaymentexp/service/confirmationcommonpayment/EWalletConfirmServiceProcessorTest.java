package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillerInfoResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.ReferenceResponse;
import com.tmb.oneapp.commonpaymentexp.model.cache.CacheMapper;
import com.tmb.oneapp.commonpaymentexp.model.cache.DepositAccountInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentConfig;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ExternalConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.BasePrepareDataConfirm;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.Balance;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.EWalletETERequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.EWalletETEResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.Receiver;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.Sender;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.Terminal;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CreditCardValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.DepositValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.ValidationCommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.model.notification.NotificationCommonPayment;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.NotificationCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomersTransactionService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.dailylimit.DailyLimitService;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import com.tmb.oneapp.commonpaymentexp.utils.TestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;

import java.math.BigDecimal;

import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_E_WALLET;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_TOPUP_E_WALLET;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class EWalletConfirmServiceProcessorTest {
    @Spy
    @InjectMocks
    private EWalletConfirmServiceProcessor eWalletConfirmServiceProcessor;
    @Mock
    private AsyncHelper asyncHelper;
    @Mock
    private PaymentService paymentService;
    @Mock
    private DailyLimitService dailyLimitService;
    @Mock
    private CustomersTransactionService customersTransactionService;
    @Mock
    private NotificationCommonPaymentService notificationCommonPaymentService;
    @Mock
    private LogEventPublisherService logEventPublisherService;
    @Mock
    private CommonValidateConfirmationService commonValidateConfirmationService;
    @Mock
    private BaseConfirmServiceHelper baseConfirmServiceHelper;

    private CommonPaymentDraftCache draftCache;
    private CustomerCrmProfile customerProfile;
    private EWalletETEResponse eteResponse;
    private String transactionId;
    private String crmId;
    private String correlationId;
    private HttpHeaders headers;
    private ConfirmationCommonPaymentRequest request;
    private static final String CALLBACK_URL = "https://api.example.com/callback";
    private static final String API_KEY = "VGhpc0lzQVZlcnlWZXJ5VmVyeVZlcnlWZXJ5TG9uZ1NlY3JldEtleUZvckhTNTEyQWxnb3JpdGhtQW5kSXRNdXN0QmU1MTJCaXRz";

    @BeforeEach
    void setUp() throws TMBCommonException {
        String ipAddress = "127.0.0.1";

        transactionId = "txn-001";
        crmId = "crm-001";
        correlationId = "corr-001";

        headers = new HttpHeaders();
        headers.set(CommonPaymentExpConstant.HEADER_CORRELATION_ID, correlationId);
        headers.set(CommonPaymentExpConstant.HEADER_CRM_ID, crmId);
        headers.set(CommonPaymentExpConstant.HEADER_IP_ADDRESS, ipAddress);
        headers.set(CommonPaymentExpConstant.HEADER_ACCEPT_LANGUAGE, "en");
        headers.set(CommonPaymentExpConstant.HEADER_APP_VERSION, "5.12.0");

        request = new ConfirmationCommonPaymentRequest();
        request.setTransactionId(transactionId);

        customerProfile = new CustomerCrmProfile();
        customerProfile.setPinFreeSeetingFlag("Y");
        customerProfile.setPinFreeTxnCount(0);

        draftCache = createDraftCache();

        eteResponse = new EWalletETEResponse();
        eteResponse.setBalance(new Balance());
        eteResponse.getBalance().setAvailable(new BigDecimal("1000.00"));

        TestUtils.setUpAsyncHelperExecuteMethodAsync(asyncHelper);
        TestUtils.setUpAsyncHelperExecuteRequestAsyncSafely(asyncHelper);
        TestUtils.setUpAsyncExecuteMethodAsyncSafelyVoid(asyncHelper);
    }

    @Test
    void testGetProcessorTypeThenReturnEWallet() {
        String result = eWalletConfirmServiceProcessor.getProcessorType();

        assertEquals(BILLER_PAYMENT_TOPUP_E_WALLET, result);
    }

    @Test
    void testConfirmWhenSuccessThenReturnResponse() throws TMBCommonException, TMBCommonExceptionWithResponse {
        draftCache.getPaymentInformation().setCompCode(BILL_COMP_CODE_E_WALLET);
        draftCache.getCommonPaymentConfig().setCallbackUrl(CALLBACK_URL);
        draftCache.getCommonPaymentConfig().setCallbackApiKey(API_KEY);
        mockGetBasePrepareDataConfirmReturnData(customerProfile);
        when(paymentService.confirmEWalletPayment(any(), any(), any(), any())).thenReturn(eteResponse);

        ConfirmationCommonPaymentResponse response = eWalletConfirmServiceProcessor.executeConfirm(request, headers, draftCache);

        assertNotNull(response);
        assertEquals(new BigDecimal("1000.00"), response.getRemainingBalance());
        assertNotNull(response.getQr());
        verify(dailyLimitService, times(1)).validateDailyLimitExceeded(eq(draftCache.getValidateDraftCache().getMasterBillerResponse().getBillerInfo().getBillerGroupType()), eq(customerProfile), eq(new BigDecimal(draftCache.getValidateDraftCache().getExternalConfirmRequest().getEWalletConfirmRequest().getAmount())));
        verify(dailyLimitService, times(1)).updateAccumulateUsage(eq(draftCache), eq(customerProfile), eq(crmId), eq(correlationId));
        verify(baseConfirmServiceHelper, times(1)).baseUpdatePinFreeCountWithCondition(eq(crmId), eq(correlationId), eq(customerProfile), eq(draftCache.getValidateDraftCache().isRequireCommonAuthen()));
        verify(dailyLimitService, times(1)).validateDailyLimitExceeded(anyString(), any(CustomerCrmProfile.class), any(BigDecimal.class));
        verify(customersTransactionService, times(1)).clearDepositCache(correlationId, crmId);
        verify(baseConfirmServiceHelper, times(1)).baseClearDraftDataCache(transactionId);
        verify(commonValidateConfirmationService, times(1)).verifyAuthentication(transactionId, draftCache, headers);
        verify(commonValidateConfirmationService, times(1)).validateTransactionByTransactionId(transactionId);
        assertCallWriteActivityLogSuccess();
        assertCallWriteFinancialLog();
        assertCallWriteTransactionLog();
        assertCallSentNotification();
    }

    @Test
    void testConfirmWhenPaymentServiceFailsThenThrowException() throws TMBCommonException, TMBCommonExceptionWithResponse {
        mockGetBasePrepareDataConfirmReturnData(customerProfile);
        when(paymentService.confirmEWalletPayment(any(), any(), any(), any())).thenThrow(new TMBCommonException(ResponseCode.FAILED_V2.getCode(), ResponseCode.FAILED_V2.getMessage(), ResponseCode.FAILED_V2.getService(), HttpStatus.OK, null));
        when(baseConfirmServiceHelper.baseHandleException(any(), any(TMBCommonException.class))).thenThrow(TMBCommonException.class);

        assertThrows(TMBCommonException.class, () -> eWalletConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

        assertCallWriteActivityLogFailed();
        assertCallWriteFinancialLog();
        assertCallWriteTransactionLog();
        verify(baseConfirmServiceHelper, times(1)).baseHandleException(any(), any(TMBCommonException.class));
    }

    @Test
    void testConfirmWhenDailyLimitExceededThenThrowException() throws TMBCommonException, TMBCommonExceptionWithResponse {
        mockGetBasePrepareDataConfirmReturnData(customerProfile);

        doThrow(new TMBCommonException(ResponseCode.DAILY_LIMIT_EXCEEDED.getCode(), ResponseCode.DAILY_LIMIT_EXCEEDED.getMessage(), ResponseCode.DAILY_LIMIT_EXCEEDED.getService(), HttpStatus.BAD_REQUEST, null)).when(dailyLimitService).validateDailyLimitExceeded(any(), any(), any());
        when(baseConfirmServiceHelper.baseHandleException(any(), any(TMBCommonException.class))).thenThrow(TMBCommonException.class);

        assertThrows(TMBCommonException.class, () -> eWalletConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

        verify(baseConfirmServiceHelper, times(1)).baseHandleException(any(), any(TMBCommonException.class));
    }

    @Test
    void testConfirmWhenAutoSaveSlipMainNotYThenReturnFalse() throws TMBCommonException, TMBCommonExceptionWithResponse {
        customerProfile.setAutoSaveSlipMain("N");
        mockGetBasePrepareDataConfirmReturnData(customerProfile);
        when(paymentService.confirmEWalletPayment(any(), any(), any(), any())).thenReturn(eteResponse);

        ConfirmationCommonPaymentResponse response = eWalletConfirmServiceProcessor.executeConfirm(request, headers, draftCache);

        assertFalse(response.isAutoSaveSlip());
    }

    @Nested
    class validateDateTests {
        @Test
        void testValidatePaymentConfirmWhenAuthenticationFailsThenThrowException() throws TMBCommonException, TMBCommonExceptionWithResponse {
            mockGetBasePrepareDataConfirmReturnData(customerProfile);
            TMBCommonException throwException = new TMBCommonException(ResponseCode.AUTHORIZE_FAILED.getCode(), ResponseCode.AUTHORIZE_FAILED.getMessage(), ResponseCode.AUTHORIZE_FAILED.getService(), HttpStatus.UNAUTHORIZED, null);
            doThrow(throwException).when(commonValidateConfirmationService).verifyAuthentication(eq(transactionId), eq(draftCache), eq(headers));
            when(baseConfirmServiceHelper.baseHandleException(any(), any(throwException.getClass()))).thenThrow(throwException);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                    eWalletConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

            assertEquals(ResponseCode.AUTHORIZE_FAILED.getCode(), exception.getErrorCode());
        }

        @Test
        void testValidatePaymentConfirmWhenTransactionValidationFailsThenThrowException() throws TMBCommonException, TMBCommonExceptionWithResponse {
            mockGetBasePrepareDataConfirmReturnData(customerProfile);
            TMBCommonException throwException = new TMBCommonException(ResponseCode.TRANSACTION_DUPLICATE_ERROR.getCode(), ResponseCode.TRANSACTION_DUPLICATE_ERROR.getMessage(), ResponseCode.TRANSACTION_DUPLICATE_ERROR.getService(), HttpStatus.BAD_REQUEST, null);
            doThrow(throwException).when(commonValidateConfirmationService).validateTransactionByTransactionId(eq(transactionId));
            when(baseConfirmServiceHelper.baseHandleException(any(), any(throwException.getClass()))).thenThrow(throwException);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                    eWalletConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

            assertEquals(ResponseCode.TRANSACTION_DUPLICATE_ERROR.getCode(), exception.getErrorCode());
            verify(commonValidateConfirmationService, times(1)).verifyAuthentication(transactionId, draftCache, headers);
        }
    }

    private CommonPaymentDraftCache createDraftCache() {
        CommonPaymentDraftCache cache = new CommonPaymentDraftCache();
        ValidationCommonPaymentDraftCache validateDraftCache = new ValidationCommonPaymentDraftCache();
        MasterBillerResponse masterBillerResponse = new MasterBillerResponse();
        BillerInfoResponse billerInfo = new BillerInfoResponse();
        PaymentInformation paymentInformation = new PaymentInformation();
        ValidationCommonPaymentRequest validateRequest = new ValidationCommonPaymentRequest();
        ProductDetail productDetail = new ProductDetail();

        EWalletETERequest eWalletConfirmRequest = new EWalletETERequest();
        eWalletConfirmRequest.setAmount("100");
        eWalletConfirmRequest.setTransactionReference("transaction-reference");
        eWalletConfirmRequest.setFee(100.50);
        eWalletConfirmRequest.setTerminal(new Terminal().setId("terminal-id"));
        eWalletConfirmRequest.setReceiver(new Receiver()
                .setAccountDisplayName("account-display-name")
                .setProxyValue("proxy-value")
                .setProxyType("proxy-type")
                .setBankCode("bank-code")
                .setAccountId("account-id"));
        eWalletConfirmRequest.setSender(new Sender()
                .setAccountId("sender-account-id")
                .setAccountType("sender-account-type")
                .setAccountName("sender-account-name"));

        billerInfo.setBillerGroupType("0");
        billerInfo.setPaymentMethod("5");

        masterBillerResponse.setBillerInfo(billerInfo);
        masterBillerResponse.setRef1(new ReferenceResponse());

        BillerInfoResponse billerInfoResponse = new BillerInfoResponse();
        billerInfoResponse.setBillerGroupType("0");

        billerInfoResponse.setPaymentMethod("5");
        billerInfoResponse.setNameEn("biller name");
        billerInfoResponse.setBillerCompCode("biller comp code");

        validateRequest.setNote("test note");

        productDetail.setProductRef1("reference1");
        productDetail.setProductRef2("reference2");

        paymentInformation.setCompCode("comp-001");
        paymentInformation.setProductDetail(productDetail);

        validateDraftCache.setExternalConfirmRequest(new ExternalConfirmRequest().setEWalletConfirmRequest(eWalletConfirmRequest));
        validateDraftCache.setMasterBillerResponse(CacheMapper.INSTANCE.toMasterBillerResponseInCache(masterBillerResponse));
        validateDraftCache.setRequireCommonAuthen(false);
        validateDraftCache.setFromDepositAccount(new DepositAccountInCache());
        validateDraftCache.setMasterBillerResponse(CacheMapper.INSTANCE.toMasterBillerResponseInCache(masterBillerResponse));
        validateDraftCache.getMasterBillerResponse().setBillerInfo(CacheMapper.INSTANCE.toBillerInfoResponseInCache(billerInfoResponse));
        validateDraftCache.setFeeCalculated(new BigDecimal("10.50"));

        CommonPaymentConfig commonPaymentConfig = new CommonPaymentConfig();

        cache.setValidateDraftCache(validateDraftCache);
        cache.setValidateRequest(validateRequest);
        cache.setPaymentInformation(paymentInformation);

        cache.getValidateRequest().setCreditCard(new CreditCardValidationCommonPaymentRequest());
        cache.getValidateRequest().setDeposit(new DepositValidationCommonPaymentRequest());
        cache.setCommonPaymentConfig(commonPaymentConfig);


        return cache;

    }

    private void mockGetBasePrepareDataConfirmReturnData(CustomerCrmProfile customerProfile) throws TMBCommonException {
        BasePrepareDataConfirm basePrepareDataConfirm = new BasePrepareDataConfirm().setCustomerCrmProfile(customerProfile).setTransactionTime(String.valueOf(System.currentTimeMillis()));
        when(baseConfirmServiceHelper.getBasePrepareDataConfirm(any(HttpHeaders.class), anyString())).thenReturn(basePrepareDataConfirm);
    }

    private void assertCallSentNotification() {
        verify(notificationCommonPaymentService, times(1)).sendENotification(any(NotificationCommonPayment.class));
    }

    private void assertCallWriteTransactionLog() {
        verify(logEventPublisherService, times(1)).saveTransactionLog(eq(correlationId), any());
    }

    private void assertCallWriteFinancialLog() {
        verify(logEventPublisherService, times(1)).saveFinancialLog(eq(correlationId), any());
    }

    private void assertCallWriteActivityLogSuccess() {
        verify(logEventPublisherService, times(1)).saveActivityLog(any(), any());
    }

    private void assertCallWriteActivityLogFailed() {
        verify(logEventPublisherService, times(1)).saveActivityLog(any());
    }

}