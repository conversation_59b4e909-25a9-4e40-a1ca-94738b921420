package com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.ocp.custombillpayment;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.cache.Transaction;
import com.tmb.common.cache.service.TransactionServices;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.model.BillerCreditcardMerchant;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.ConversionRateDetail;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.WowPoint;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.validation.ActivityBillPayValidationEvent;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillPayConfiguration;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillerInfoResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.ReferenceResponse;
import com.tmb.oneapp.commonpaymentexp.model.cache.CommonPaymentRuleInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenResult;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.AmountDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentConfig;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CompleteScreenDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PhraseDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.AdditionalParam;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPAccountPayment;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBalance;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPayment;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPFee;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.BillPayValidateDataAfterCallExternal;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CommonAuthenticationValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CreditCardValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.DepositValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.BillPayExternalValidateResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.BillPayPrepareDataValidate;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CardBalancesAsync;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardSupplementary;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.ocp.OCPValidationHelper;
import com.tmb.oneapp.commonpaymentexp.validator.BaseBillPayValidator;
import com.tmb.oneapp.commonpaymentexp.validator.DailyLimitPinFreeValidator;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;

import java.math.BigDecimal;
import java.util.List;

import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_GROUP_TYPE_BILL;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_MWA;
import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_HASH_KEY_CACHE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_AUTH_BILL_FLOW_NAME;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_PAYMENT_BILL_PAY_FEATURE_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_MWA;
import static com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService.COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class MWABillPayValidationProcessorTest {
    @InjectMocks
    private MWABillPayValidationProcessor mwaBillPayValidationProcessor;


    @Mock
    private TransactionServices transactionServices;
    @Mock
    private PaymentService paymentService;
    @Mock
    private LogEventPublisherService logEventPublisherService;
    @Mock
    private CacheService cacheService;
    @Mock
    private DailyLimitPinFreeValidator dailyLimitPinFreeValidator;
    @Mock
    private BaseBillPayValidator baseBillPayValidator;
    @Mock
    private OCPValidationHelper ocpValidationHelper;

    private String crmId;
    private String correlationId;
    private HttpHeaders headers;
    private ValidationCommonPaymentRequest request;
    private CommonPaymentDraftCache cacheData;
    private CommonPaymentConfig commonPaymentConfig;
    private String compCode;
    private String depositAccountNumber;

    @BeforeEach
    void setUp() {
        Transaction.setTransactionServices(transactionServices);

        crmId = "crmId";
        correlationId = "correlationId";
        headers = new HttpHeaders();
        headers.add(CommonPaymentExpConstant.HEADER_CRM_ID, crmId);
        headers.add(CommonPaymentExpConstant.HEADER_CORRELATION_ID, correlationId);

        cacheData = new CommonPaymentDraftCache();
        compCode = BILL_COMP_CODE_MWA;
        depositAccountNumber = "**********";
        request = new ValidationCommonPaymentRequest();
        commonPaymentConfig = new CommonPaymentConfig();
    }

    @Test
    void testGetProcessorTypeWhenCalledThenReturnMWABillPayment() {
        String actual = mwaBillPayValidationProcessor.getProcessorType();
        assertEquals(BILLER_PAYMENT_MWA, actual);
    }

    @ParameterizedTest
    @CsvSource({
            "9500.50, 9500.50, false",
            "9500.50, 9500.50, true"
    })
    void testValidateAmountWhenAmountMatchThenDoesNotThrowException(BigDecimal requestAmount, String ocpAmount, boolean isPayWithCreditCard) {
        ValidationCommonPaymentRequest request = new ValidationCommonPaymentRequest();
        if (isPayWithCreditCard) {
            request.setCreditCard(new CreditCardValidationCommonPaymentRequest().setAmount(requestAmount).setPayWithCreditCardFlag(isPayWithCreditCard));
        } else {
            request.setDeposit(new DepositValidationCommonPaymentRequest().setAmount(requestAmount));
        }

        OCPBillPayment ocpDataResponse = new OCPBillPayment();
        OCPBalance ocpBalance = new OCPBalance();
        ocpBalance.setMax(ocpAmount);
        ocpDataResponse.setBalance(ocpBalance);

        Assertions.assertDoesNotThrow(() ->
                mwaBillPayValidationProcessor.validateAmount(request, ocpDataResponse)
        );
    }

    @ParameterizedTest
    @CsvSource({
            "9500.50, 9600.50, false",
            "9500.50, 9400.50, true"
    })
    void testValidateAmountWhenAmountNotMatchThenThrowTMBCommonException(BigDecimal requestAmount, String ocpAmount, boolean isPayWithCreditCard) {
        ValidationCommonPaymentRequest request = new ValidationCommonPaymentRequest();
        if (isPayWithCreditCard) {
            request.setCreditCard(new CreditCardValidationCommonPaymentRequest().setAmount(requestAmount).setPayWithCreditCardFlag(isPayWithCreditCard));
        } else {
            request.setDeposit(new DepositValidationCommonPaymentRequest().setAmount(requestAmount));
        }

        OCPBillPayment ocpDataResponse = new OCPBillPayment();
        OCPBalance ocpBalance = new OCPBalance();
        ocpBalance.setMax(ocpAmount);
        ocpDataResponse.setBalance(ocpBalance);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                mwaBillPayValidationProcessor.validateAmount(request, ocpDataResponse)
        );

        assertEquals(ResponseCode.MWA_AMOUNT_NOT_FOUND_ERROR.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
    }

    @Test
    void testValidateAmountWhenBalanceIsNullThenThrowTMBCommonException() {
        ValidationCommonPaymentRequest request = new ValidationCommonPaymentRequest();
        request.setDeposit(new DepositValidationCommonPaymentRequest().setAmount(BigDecimal.valueOf(9500.50)));

        OCPBillPayment ocpDataResponse = new OCPBillPayment();
        ocpDataResponse.setBalance(null);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                mwaBillPayValidationProcessor.validateAmount(request, ocpDataResponse)
        );

        assertEquals(ResponseCode.MWA_AMOUNT_NOT_FOUND_ERROR.getCode(), exception.getErrorCode());
    }

    @Test
    void testExecuteBillPayWhenBillPayTransactionAndPayWithDepositThenSuccess() throws TMBCommonException, TMBCustomCommonExceptionWithResponse, TMBCommonExceptionWithResponse, JsonProcessingException {
        cacheData = initialCacheDataForBillPayTransaction();
        cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY);
        request = initialDefaultRequest();

        mockBasePrepareDataReturnData();
        mockValidateDailyLimitExceededDoNothing();
        mockStaticTransactionGenerateId();
        mockValidateOCPBillPayment();
        mockValidateRequireConfirmPinReturnTrue();

        ValidationCommonPaymentResponse actual = mwaBillPayValidationProcessor.executeValidate(request, headers, cacheData);

        assertNotNull(actual.getFee());
        assertNotNull(actual.getTransactionId());
        assertNotNull(actual.getTotalAmount());
        assertTrue(actual.getIsRequireCommonAuthen());
        assertEquals(COMMON_PAYMENT_BILL_PAY_FEATURE_ID, actual.getCommonAuthenticationInformation().getFeatureId());
        assertEquals(COMMON_AUTH_BILL_FLOW_NAME, actual.getCommonAuthenticationInformation().getFlowName());
        assertNotNull(actual.getCommonAuthenticationInformation().getTotalPaymentAccumulateUsage());
        assertNotNull(actual.getMwaValidationCommonPaymentResponse());
        assertNotNull(actual.getMwaValidationCommonPaymentResponse().getBill());
        assertNotNull(cacheData.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest().getBalance());
        assertEquals("ocp_response_payment_id", cacheData.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest().getPaymentId());
        verify(logEventPublisherService, times(1)).saveActivityLog(any(ActivityBillPayValidationEvent.class));
        verify(cacheService, times(1)).set(anyString(), eq(COMMON_PAYMENT_HASH_KEY_CACHE), any(CommonPaymentDraftCache.class));
    }

    @Test
    void testExecuteBillPayWhenBillPayTransactionAndPayWithCreditCardThenSuccess() throws TMBCommonException, TMBCustomCommonExceptionWithResponse, TMBCommonExceptionWithResponse {
        cacheData = initialCacheDataForBillPayTransaction();
        cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY);
        request = initialDefaultRequest();
        request.getDeposit().setPayWithDepositFlag(false).setAmount(null).setAccountNumber(null);
        request.setCreditCard(
                new CreditCardValidationCommonPaymentRequest()
                        .setPayWithCreditCardFlag(true)
                        .setAmount(BigDecimal.valueOf(9500.50))
                        .setAccountId("0000000050083520705000171")
        );

        mockBasePrepareDataReturnData();
        mockStaticTransactionGenerateId();
        mockValidateOCPBillPayment();
        mockValidateRequireConfirmPinReturnTrue();

        ValidationCommonPaymentResponse actual = mwaBillPayValidationProcessor.executeValidate(request, headers, cacheData);

        assertNotNull(actual.getFee());
        assertNotNull(actual.getTransactionId());
        assertNotNull(actual.getTotalAmount());
        assertTrue(actual.getIsRequireCommonAuthen());
        assertEquals(COMMON_PAYMENT_BILL_PAY_FEATURE_ID, actual.getCommonAuthenticationInformation().getFeatureId());
        assertEquals(COMMON_AUTH_BILL_FLOW_NAME, actual.getCommonAuthenticationInformation().getFlowName());
        assertNotNull(actual.getCommonAuthenticationInformation().getTotalPaymentAccumulateUsage());
        assertNotNull(actual.getMwaValidationCommonPaymentResponse());
        assertNotNull(actual.getMwaValidationCommonPaymentResponse().getBill());
    }

    @Test
    void testMappingResponseWhenRequireConfirmPinIsTrueThenSuccess() {
        ValidationCommonPaymentRequest request = new ValidationCommonPaymentRequest();
        request.setTransactionId("transaction-id");
        request.setCreditCard(new CreditCardValidationCommonPaymentRequest());

        HttpHeaders headers = new HttpHeaders();
        PaymentInformation paymentInfo = new PaymentInformation();
        paymentInfo.setCompCode("1234");
        CommonPaymentDraftCache cache = new CommonPaymentDraftCache();
        cache.setPaymentInformation(paymentInfo);

        BillPayPrepareDataValidate prepareData = new BillPayPrepareDataValidate();

        BillPayExternalValidateResponse externalResponse = new BillPayExternalValidateResponse();
        OCPBillPayment ocpDataResponse = new OCPBillPayment();
        ocpDataResponse.setAmount("9500.50");
        List<AdditionalParam> additionalParams = List.of(
                new AdditionalParam().setName("Message").setValue("1|xxx|123456|100|9500.50")
        );
        ocpDataResponse.setAdditionalParams(additionalParams);
        externalResponse.setOcpDataResponse(ocpDataResponse);

        BillPayValidateDataAfterCallExternal validateDataAfterCallExternal = new BillPayValidateDataAfterCallExternal();
        validateDataAfterCallExternal.setFeeAfterCalculated(BigDecimal.valueOf(10.50));
        validateDataAfterCallExternal.setTotalAmount(new BigDecimal("9511.00"));
        validateDataAfterCallExternal.setRequireCommonAuthen(true);
        validateDataAfterCallExternal.setCommonAuthentication(new CommonAuthenticationValidationCommonPaymentResponse().setFeatureId("feature-id"));

        ValidationCommonPaymentResponse response = mwaBillPayValidationProcessor.mappingResponse(
                request, headers, cache, prepareData, externalResponse, validateDataAfterCallExternal);

        assertEquals("transaction-id", response.getTransactionId());
        assertEquals(BigDecimal.valueOf(10.50), response.getFee());
        assertEquals(new BigDecimal("9500.50"), response.getAmount());
        assertEquals(new BigDecimal("9511.00"), response.getTotalAmount());
        assertTrue(response.getIsRequireCommonAuthen());
        assertEquals("feature-id", response.getCommonAuthenticationInformation().getFeatureId());
        assertNotNull(response.getMwaValidationCommonPaymentResponse());
        assertEquals(1, response.getMwaValidationCommonPaymentResponse().getBill().size());
        assertEquals("123456", response.getMwaValidationCommonPaymentResponse().getBill().get(0).getBillNumber());
        assertEquals("100.00", response.getMwaValidationCommonPaymentResponse().getBill().get(0).getVat());
        assertEquals("9500.50", response.getMwaValidationCommonPaymentResponse().getBill().get(0).getAmount());
    }

    @Test
    void testMappingResponseWhenRequireConfirmPinIsFalseThenSuccess() {
        ValidationCommonPaymentRequest request = new ValidationCommonPaymentRequest();
        request.setTransactionId("transaction-id");
        request.setCreditCard(new CreditCardValidationCommonPaymentRequest());

        HttpHeaders headers = new HttpHeaders();
        PaymentInformation paymentInfo = new PaymentInformation();
        paymentInfo.setCompCode("1234");
        CommonPaymentDraftCache cache = new CommonPaymentDraftCache();
        cache.setPaymentInformation(paymentInfo);
        BillPayPrepareDataValidate prepareData = new BillPayPrepareDataValidate();

        BillPayExternalValidateResponse externalResponse = new BillPayExternalValidateResponse();
        OCPBillPayment ocpDataResponse = new OCPBillPayment();
        ocpDataResponse.setAmount("9500.50");
        List<AdditionalParam> additionalParams = List.of(
                new AdditionalParam().setName("Message").setValue("1|xxx|123456|100|9500.50")
        );
        ocpDataResponse.setAdditionalParams(additionalParams);
        externalResponse.setOcpDataResponse(ocpDataResponse);

        BillPayValidateDataAfterCallExternal validateDataAfterCallExternal = new BillPayValidateDataAfterCallExternal();
        validateDataAfterCallExternal.setFeeAfterCalculated(BigDecimal.valueOf(10.50));
        validateDataAfterCallExternal.setTotalAmount(new BigDecimal("9511.00"));
        validateDataAfterCallExternal.setRequireCommonAuthen(false);
        validateDataAfterCallExternal.setCommonAuthentication(new CommonAuthenticationValidationCommonPaymentResponse().setFeatureId("feature-id"));

        ValidationCommonPaymentResponse response = mwaBillPayValidationProcessor.mappingResponse(
                request, headers, cache, prepareData, externalResponse, validateDataAfterCallExternal);

        assertEquals("transaction-id", response.getTransactionId());
        assertEquals(BigDecimal.valueOf(10.50), response.getFee());
        assertEquals(new BigDecimal("9500.50"), response.getAmount());
        assertEquals(new BigDecimal("9511.00"), response.getTotalAmount());
        assertEquals(false, response.getIsRequireCommonAuthen());
        assertNull(response.getCommonAuthenticationInformation());
        assertNotNull(response.getMwaValidationCommonPaymentResponse());
        assertEquals(1, response.getMwaValidationCommonPaymentResponse().getBill().size());
        assertEquals("123456", response.getMwaValidationCommonPaymentResponse().getBill().get(0).getBillNumber());
        assertEquals("100.00", response.getMwaValidationCommonPaymentResponse().getBill().get(0).getVat());
        assertEquals("9500.50", response.getMwaValidationCommonPaymentResponse().getBill().get(0).getAmount());
    }

    @Nested
    class BillPayWithWowPointTransactionTest {
        @Test
        void testExecuteBillPay_WhenBillPayTransactionAndPayWithDepositAndWowPoint_ShouldSuccess() throws TMBCommonException, TMBCommonExceptionWithResponse {
            CommonPaymentRuleInCache commonPaymentRule = new CommonPaymentRuleInCache();
            commonPaymentRule.setWowPointFlag(true);
            commonPaymentRule.setWowPoint(new WowPoint()
                    .setMinPaymentAmount(new BigDecimal(100))
                    .setMin(new BigDecimal(10))
                    .setMax(new BigDecimal(10000))
                    .setConversionRate(new ConversionRateDetail(new BigDecimal(2), new BigDecimal(15))));

            cacheData = initialCacheDataForBillPayTransaction();
            cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY);
            cacheData.setCommonPaymentRule(commonPaymentRule);

            request = initialDefaultRequest();
            request.setWowPoint(new WowPointValidationCommonPaymentRequest()
                    .setWowPointAmount(new BigDecimal(1500))
                    .setDiscountAmount(new BigDecimal(200)));

            mockBasePrepareDataReturnData();
            mockValidateDailyLimitExceededDoNothing();
            mockStaticTransactionGenerateId();
            mockValidateOCPBillPayment();
            mockValidateRequireConfirmPinReturnTrue();

            Assertions.assertDoesNotThrow(() -> mwaBillPayValidationProcessor.executeValidate(request, headers, cacheData));
            verify(baseBillPayValidator, times(1)).validateWowPoint(request, cacheData);
        }

        @Test
        void testExecuteBillPay_WhenValidateNormallyBillPayTransaction_ShouldSuccess() throws TMBCommonException, TMBCustomCommonExceptionWithResponse, TMBCommonExceptionWithResponse {
            cacheData = initialCacheDataForBillPayTransaction();
            cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY);
            CommonPaymentRuleInCache commonPaymentRule = new CommonPaymentRuleInCache();
            commonPaymentRule.setWowPointFlag(true);
            commonPaymentRule.setWowPoint(new WowPoint()
                    .setMinPaymentAmount(new BigDecimal(100))
                    .setMin(new BigDecimal(10))
                    .setMax(new BigDecimal(10000))
                    .setConversionRate(new ConversionRateDetail(new BigDecimal(2), new BigDecimal(15))));
            cacheData.setCommonPaymentRule(commonPaymentRule);

            request = initialDefaultRequest();
            request.setWowPoint(null);

            mockBasePrepareDataReturnData();
            mockValidateDailyLimitExceededDoNothing();
            mockStaticTransactionGenerateId();
            mockValidateOCPBillPayment();
            mockValidateRequireConfirmPinReturnTrue();

            ValidationCommonPaymentResponse actual = mwaBillPayValidationProcessor.executeValidate(request, headers, cacheData);

            assertNotNull(actual.getFee());
            assertNotNull(actual.getTransactionId());
            assertNotNull(actual.getTotalAmount());
            assertTrue(actual.getIsRequireCommonAuthen());
            assertEquals(COMMON_PAYMENT_BILL_PAY_FEATURE_ID, actual.getCommonAuthenticationInformation().getFeatureId());
            assertEquals(COMMON_AUTH_BILL_FLOW_NAME, actual.getCommonAuthenticationInformation().getFlowName());
            assertNotNull(actual.getCommonAuthenticationInformation().getTotalPaymentAccumulateUsage());
        }

        @Test
        void testExecuteBillPay_WhenFailedValidateWowPoint_ShouldThrowTMBCommonException() throws TMBCommonException {
            CommonPaymentRuleInCache commonPaymentRule = new CommonPaymentRuleInCache();
            commonPaymentRule.setWowPointFlag(true);
            commonPaymentRule.setWowPoint(new WowPoint()
                    .setMinPaymentAmount(new BigDecimal(10))
                    .setMin(new BigDecimal(10))
                    .setMax(new BigDecimal(100))
                    .setConversionRate(new ConversionRateDetail(new BigDecimal(1), new BigDecimal(10))));

            WowPointValidationCommonPaymentRequest invalidWowPoint = new WowPointValidationCommonPaymentRequest()
                    .setWowPointAmount(new BigDecimal(1000))
                    .setDiscountAmount(new BigDecimal(100));

            cacheData = initialCacheDataForBillPayTransaction();
            cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY);
            cacheData.setCommonPaymentRule(commonPaymentRule);

            request = initialDefaultRequest();
            request.getDeposit().setAmount(BigDecimal.valueOf(101));
            request.setWowPoint(invalidWowPoint);

            mockBasePrepareDataReturnData();
            mockValidateDailyLimitExceededDoNothing();
            doThrow(TMBCommonException.class).when(baseBillPayValidator).validateWowPoint(request, cacheData);

            assertThrows(TMBCommonException.class, () -> mwaBillPayValidationProcessor.executeValidate(request, headers, cacheData));
        }
    }

    @Nested
    class HandleExceptionTest {
        @Test
        void testHandleException_WhenFailedTMBCommonException_ShouldThrowTMBCommonException() {
            assertThrows(TMBCommonException.class, () -> mwaBillPayValidationProcessor.handleException(null, null, null, null, null, null, new TMBCommonException("ignore message")));
        }

        @Test
        void testHandleException_WhenFailedTMBCommonExceptionWithResponse_ShouldThrowTMBCommonExceptionWithResponse() {
            assertThrows(TMBCommonExceptionWithResponse.class, () -> mwaBillPayValidationProcessor.handleException(null, null, null, null, null, null, new TMBCommonExceptionWithResponse("", "", "", HttpStatus.OK, null, null)));
        }
    }

    private CommonPaymentDraftCache initialCacheDataForBillPayTransaction() {
        PaymentInformation p = new PaymentInformation()
                .setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY)
                .setTransactionType("bill_pay")
                .setCompCode(compCode)
                .setFundCode("ignore")
                .setRequireAddressFlag(true)
                .setProductDetail(new ProductDetail()
                        .setProductNameEn("Product-name-en-value")
                        .setProductNameTh("Product-name-th-value")
                        .setProductAttributeList(List.of(
                                new PhraseDetail()
                                        .setLabelEn("label-en-1")
                                        .setLabelTh("label-th-1")
                                        .setValueEn("1000000006")
                                        .setValueTh("1000000006"),
                                new PhraseDetail()
                                        .setLabelEn("label-en-1")
                                        .setLabelTh("label-th-1")
                                        .setValueEn("0987834783")
                                        .setValueTh("0987834783")
                        ))
                        .setProductRef1("reference1")
                        .setProductRef2("reference2")
                )
                .setAmountDetail(new AmountDetail()
                        .setAmountLabelEn("Amount-lable-en-value")
                        .setAmountLabelTh("Amount-lable-th-value")
                        .setAmountUnitEn("Amount-unit-en-value")
                        .setAmountUnitTh("Amount-unit-th-value")
                        .setAmountValue(BigDecimal.valueOf(9400.50))
                        .setPaymentAmount(BigDecimal.valueOf(9500.50))
                        .setVat(BigDecimal.valueOf(100.00))
                )
                .setCompleteScreenDetail(new CompleteScreenDetail()
                        .setRemarkEn("Remark-en-value")
                        .setRemarkTh("Remark-th-value")
                        .setFooterEn("Footer-en-value")
                        .setFooterTh("Footer-th-value")
                        .setBackBtnKeyEn("Back-btn-key-en-value")
                        .setBackBtnKeyTh("Back-btn-key-th-value")
                        .setBackBtnUrl("www.google.co.th")
                );

        return new CommonPaymentDraftCache()
                .setPaymentInformation(p)
                .setCrmId(crmId)
                .setCommonPaymentConfig(commonPaymentConfig);
    }

    private void mockValidateDailyLimitExceededDoNothing() throws TMBCommonException {
        doNothing().when(baseBillPayValidator).validateDailyLimit(any(ValidationCommonPaymentRequest.class), any(MasterBillerResponse.class), any(CustomerCrmProfile.class));
    }

    private void mockStaticTransactionGenerateId() {
        when(Transaction.getTransactionId(anyString(), anyInt())).thenReturn("transaction-after-generate");
    }

    private void mockValidateRequireConfirmPinReturnTrue() throws TMBCommonException {
        when(dailyLimitPinFreeValidator.validateIsRequireCommonAuthForBill(any(HttpHeaders.class), any(BigDecimal.class), anyBoolean(), any(CustomerCrmProfile.class))).thenReturn(new CommonAuthenResult().setRequireCommonAuthen(true));
    }

    private void mockValidateOCPBillPayment() throws TMBCommonException, TMBCommonExceptionWithResponse {
        OCPBillPaymentResponse ocpBillPaymentResponse = new OCPBillPaymentResponse();
        OCPBillPayment ocpResponse = new OCPBillPayment();
        ocpResponse.setToAccount(new OCPAccountPayment()
                .setAccountId("**********")
                .setAccountType("CDA")
                .setFiId("")
                .setTitle(null)
        );
        ocpResponse.setFromAccount(new OCPAccountPayment()
                .setAccountId("**********")
                .setAccountType("SDA")
                .setFiId("")
                .setTitle(null)
        );
        ocpResponse.setAdditionalParams(List.of(
                new AdditionalParam().setName("Message").setValue("1|xxx|123456|100|9500.50"),
                new AdditionalParam().setName("CustName").setValue("CustName"),
                new AdditionalParam().setName("CustAddress").setValue("CustAddress"),
                new AdditionalParam().setName("CustAddress").setValue("CustAddress"),
                new AdditionalParam().setName("UI").setValue("********")
        ));
        OCPBalance ocpBalance = new OCPBalance();
        ocpBalance.setMax("9500.50");
        ocpResponse.setBalance(ocpBalance);
        ocpResponse.setAmount("9500.50");
        ocpResponse.setRef1("Ref1");
        ocpResponse.setRef2("Ref2");
        ocpResponse.setRef3("Ref3");
        ocpResponse.setRef4("Ref4");
        ocpResponse.setBankRefId("BankRefId");
        ocpResponse.setFee(new OCPFee().setPaymentFee("10.50"));
        ocpResponse.setPaymentId("ocp_response_payment_id");

        ocpBillPaymentResponse.setData(ocpResponse);
        when(paymentService.validateOCPBillPayment(anyString(), anyString(), any(OCPBillRequest.class))).thenReturn(ocpBillPaymentResponse);
    }

    private void mockBasePrepareDataReturnData() throws TMBCommonException {
        var prepareData = BillPayPrepareDataValidate.builder()
                .billPayConfiguration(initialBillPayConfig())
                .masterBillerResponse(initialMasterBiller())
                .fromDepositAccount(initialDepositAccount())
                .fromCreditCardDetail(initialCreditCardDetails())
                .customerCrmProfile(initialCustomerCrmProfile())
                .build();

        when(ocpValidationHelper.basePrepareData(request, headers, cacheData)).thenReturn(prepareData);
    }

    private BillPayConfiguration initialBillPayConfig() {
        BillerCreditcardMerchant billerCreditcardMerchant = new BillerCreditcardMerchant();
        billerCreditcardMerchant.setMerchantId("merchantId");
        return new BillPayConfiguration().setBillerCreditcardMerchant(List.of(billerCreditcardMerchant));
    }

    private MasterBillerResponse initialMasterBiller() {
        return new MasterBillerResponse().setBillerInfo(
                        new BillerInfoResponse()
                                .setStartTime(null)
                                .setEndTime(null)
                                .setExpiredDate("9999-12-31T00:00:00.000000+07:00")
                                .setPaymentMethod("5")
                                .setBillerMethod("0")
                                .setBillerGroupType(BILLER_GROUP_TYPE_BILL)
                                .setNameEn("PB_PRUDENTIAL LIFE ASSURANCE (THAILAND) PCL")
                                .setBillerCategoryCode("03")
                                .setFee(new BigDecimal("10.00"))
                )
                .setRef1(new ReferenceResponse().setIsMobile(false));
    }

    private DepositAccount initialDepositAccount() {
        return new DepositAccount()
                .setAccountNumber(depositAccountNumber)
                .setAccountType("SDA")
                .setWaiveFeeForBillpay("1")
                .setAvailableBalance(BigDecimal.valueOf(999999.99));
    }

    private CustomerCrmProfile initialCustomerCrmProfile() {
        return new CustomerCrmProfile()
                .setPaymentAccuUsgAmt(BigDecimal.valueOf(100.50))
                .setBillpayAccuUsgAmt(BigDecimal.valueOf(2000.50))
                .setEbAccuUsgAmtDaily(2000.50);
    }

    private CreditCardSupplementary initialCreditCardDetails() {
        CreditCardSupplementary creditCardDetail = new CreditCardSupplementary();
        creditCardDetail.setCardNo("CardNo");
        creditCardDetail.setAccountId("0000000050083520705000171");
        creditCardDetail.setExpiredBy("ExpiredBy");
        creditCardDetail.setCardEmbossingName1("cardEmbossingName1");
        creditCardDetail.setProductCode("ProductId");
        creditCardDetail.setCardBalances(new CardBalancesAsync());
        creditCardDetail.getCardBalances().setAvailableCreditAllowance(BigDecimal.valueOf(999999.99));
        return creditCardDetail;
    }

    private ValidationCommonPaymentRequest initialDefaultRequest() {
        request = new ValidationCommonPaymentRequest();
        request.setDeposit(new DepositValidationCommonPaymentRequest().setPayWithDepositFlag(true).setAccountNumber(depositAccountNumber).setAmount(BigDecimal.valueOf(9500.50)));
        request.setCreditCard(new CreditCardValidationCommonPaymentRequest().setPayWithCreditCardFlag(false));
        request.setFlow("flow");
        request.setTransactionId("transaction_id");
        return request;
    }
}