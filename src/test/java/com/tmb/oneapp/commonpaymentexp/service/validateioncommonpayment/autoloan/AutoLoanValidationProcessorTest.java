package com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.autoloan;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.tmb.common.cache.Transaction;
import com.tmb.common.cache.service.TransactionServices;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.commonpaymentexp.client.CustomerServiceClient;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.LoanAccount;
import com.tmb.oneapp.commonpaymentexp.model.ThrowableSupplier;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillPayConfiguration;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillerInfoResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.ReferenceResponse;
import com.tmb.oneapp.commonpaymentexp.model.cache.DeepLinkRequestInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenResult;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.AmountDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.CacheResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.AutoLoanPaymentDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.AutoLoanTBankCustomer;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.DeepLinkRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpETEPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpETEResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpETETransaction;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CreditCardValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.DepositValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.ValidationCommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CardBalancesAsync;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardSupplementary;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerKYCResponse;
import com.tmb.oneapp.commonpaymentexp.service.AccountCreditCardService;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.AccountCommonPaymentHelper;
import com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.BillPayAccountCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomerService;
import com.tmb.oneapp.commonpaymentexp.service.client.HpExpService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.dailylimit.DailyLimitService;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.ServiceHoursUtils;
import com.tmb.oneapp.commonpaymentexp.utils.TestUtils;
import com.tmb.oneapp.commonpaymentexp.validator.BaseBillPayValidator;
import com.tmb.oneapp.commonpaymentexp.validator.DailyLimitPinFreeValidator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.function.Supplier;

import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_CALL_FROM_AUTO_LOAN;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_CATEGORY_CODE_CREDIT_CARD;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_GROUP_TYPE_BILL;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_TTB_DRIVE_CAR_LOAN;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_AUTH_BILL_FLOW_NAME;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_PAYMENT_BILL_PAY_FEATURE_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_ACCEPT_LANGUAGE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_APP_VERSION;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_PRE_LOGIN;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_TMB_AUTO_LOAN;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AutoLoanValidationProcessorTest {

    private static final String DEFAULT_TRANSACTION_ID = "transaction_id";
    private static final String DEFAULT_DEPOSIT_ACCOUNT = "*************45";
    private static final String DEFAULT_CREDIT_CARD_ID = "0000000050083520705000171";
    private static final String DEFAULT_REF1 = "*************45";
    private static final String DEFAULT_REF2 = "123456";
    private static final BigDecimal DEFAULT_PAYMENT_AMOUNT = BigDecimal.valueOf(9500.50);

    @InjectMocks
    private AutoLoanValidationProcessor autoLoanValidationProcessor;

    @Mock
    private AsyncHelper asyncHelper;
    @Mock
    private BillPayAccountCommonPaymentService billPayAccountCommonPaymentService;
    @Mock
    private CacheService cacheService;
    @Mock
    private DailyLimitPinFreeValidator dailyLimitPinFreeValidator;
    @Mock
    private LogEventPublisherService logEventPublisherService;
    @Mock
    private PaymentService paymentService;
    @Mock
    private CustomerServiceClient customerServiceClient;
    @Mock
    private AccountCommonPaymentHelper accountCommonPaymentHelper;
    @Mock
    private DailyLimitService dailyLimitService;
    @Mock
    private AccountCreditCardService accountCreditCardService;
    @Mock
    private CustomerService customerService;
    @Mock
    private HpExpService hpExpService;
    @Mock
    private TransactionServices transactionServices;
    @Mock
    private BaseBillPayValidator baseBillPayValidator;

    private String crmId;
    private String correlationId;
    private String acceptLanguage;
    private String appVersion;
    private HttpHeaders headers;
    private ValidationCommonPaymentRequest request;
    private CommonPaymentDraftCache cacheData;
    private String depositAccountNumber;

    @BeforeEach
    void setUp() {
        crmId = "crmId";
        correlationId = "correlationId";
        acceptLanguage = "en";
        appVersion = "5.12.0";

        headers = new HttpHeaders();
        headers.add(HEADER_CRM_ID, crmId);
        headers.add(HEADER_CORRELATION_ID, correlationId);
        headers.add(HEADER_ACCEPT_LANGUAGE, acceptLanguage);
        headers.add(HEADER_APP_VERSION, appVersion);
        headers.add(HEADER_PRE_LOGIN, "false");

        cacheData = new CommonPaymentDraftCache();
        depositAccountNumber = DEFAULT_DEPOSIT_ACCOUNT;
        request = new ValidationCommonPaymentRequest();
        Transaction.setTransactionServices(transactionServices);
    }


    @Test
    void testGivenProcessorWhenGetProcessorTypeThenReturnAutoLoan() {
        String actual = autoLoanValidationProcessor.getProcessorType();
        assertEquals(BILLER_PAYMENT_TMB_AUTO_LOAN, actual);
    }


    @Test
    void testGivenValidDepositPaymentWhenExecuteThenSuccess() throws TMBCommonException, JsonProcessingException, TMBCommonExceptionWithResponse {
        setupForSuccessCase();
        mockGetAccountByAccountNumber();
        mockStaticTransactionGenerateId();
        mockDeepLinkWithAmount(DEFAULT_PAYMENT_AMOUNT.toString(), DEFAULT_PAYMENT_AMOUNT.doubleValue());

        ValidationCommonPaymentResponse actual = autoLoanValidationProcessor.executeValidate(request, headers, cacheData);

        assertSuccessfulResponse(actual);
    }

    @Test
    void testGivenValidDepositPaymentWhenPayWithOwnExecuteThenSuccess() throws TMBCommonException, TMBCommonExceptionWithResponse {
        setupForSuccessCase();
        String ownAccountHpAndRef1AreSame = "*********";
        cacheData.getPaymentInformation().getProductDetail().setProductRef1(ownAccountHpAndRef1AreSame);
        mockGetAccountByAccountNumber();
        mockStaticTransactionGenerateId();
        mockDeepLinkWithAmount(DEFAULT_PAYMENT_AMOUNT.toString(), DEFAULT_PAYMENT_AMOUNT.doubleValue());

        ValidationCommonPaymentResponse actual = autoLoanValidationProcessor.executeValidate(request, headers, cacheData);

        assertSuccessfulResponse(actual);
        verify(dailyLimitService).validateDailyLimitExceeded(anyString(), any(CustomerCrmProfile.class), any(BigDecimal.class));
    }

    @Test
    void testGivenValidCreditCardPaymentWhenExecuteThenSuccess() throws TMBCommonException, JsonProcessingException, TMBCommonExceptionWithResponse {
        setupForSuccessCase();
        mockGetCreditCardAccountWithAccountId();
        mockStaticTransactionGenerateId();
        setupCreditCardPayment();
        mockDeepLinkWithAmount(DEFAULT_PAYMENT_AMOUNT.toString(), DEFAULT_PAYMENT_AMOUNT.doubleValue());

        ValidationCommonPaymentResponse actual = autoLoanValidationProcessor.executeValidate(request, headers, cacheData);

        assertSuccessfulResponse(actual);
        assertShouldNotValidateDailyLimit();
    }

    @Test
    void testGivenExpiredBillerWhenExecuteThenThrowBillerExpiredException() throws TMBCommonException {
        cacheData = initializeCacheDataForAutoLoan();
        request = initializeDefaultRequest();
        setupBasicMocks();
        mockGetAccountByAccountNumber();
        mockExpiredBiller();
        initialDeepLinkRequestInCache();

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> autoLoanValidationProcessor.executeValidate(request, headers, cacheData));

        assertEquals(ResponseCode.BILLER_EXPIRED.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testGivenOutsideServiceHoursWhenExecuteThenThrowUnavailableServiceHourException() throws TMBCommonException {
        cacheData = initializeCacheDataForAutoLoan();
        request = initializeDefaultRequest();
        setupBasicMocks();
        mockGetAccountByAccountNumber();
        doThrow(CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.UNAVAILABLE_SERVICE_HOUR)).when(baseBillPayValidator).validateServiceHours(any(MasterBillerResponse.class));
        initialDeepLinkRequestInCache();

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> autoLoanValidationProcessor.executeValidate(request, headers, cacheData));

        assertEquals(ResponseCode.UNAVAILABLE_SERVICE_HOUR.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.OK, exception.getStatus());

    }

    @Test
    void testGivenInsufficientDepositFundsWhenExecuteThenThrowInsufficientFundsException() throws TMBCommonException, TMBCommonExceptionWithResponse {
        cacheData = initializeCacheDataForAutoLoan();
        request = initializeDefaultRequest();
        setupBasicMocks();

        mockGetAccountByAccountNumber();
        mockValidateAutoLoanPayment();
        mockStaticTransactionGenerateId();
        mockDeepLinkWithAmount(DEFAULT_PAYMENT_AMOUNT.toString(), DEFAULT_PAYMENT_AMOUNT.doubleValue());
        request.getDeposit().setAmount(BigDecimal.valueOf(***********.99));
        doThrow(new TMBCommonException(ResponseCode.DEPOSIT_ACCOUNT_INSUFFICIENT_FUNDS_ERROR.getCode(), ResponseCode.DEPOSIT_ACCOUNT_INSUFFICIENT_FUNDS_ERROR.getMessage(), ResponseCode.DEPOSIT_ACCOUNT_INSUFFICIENT_FUNDS_ERROR.getService(), HttpStatus.OK, null)).when(baseBillPayValidator).validateInsufficientFund(any(), any(), any(), any());

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> autoLoanValidationProcessor.executeValidate(request, headers, cacheData));

        assertEquals(ResponseCode.DEPOSIT_ACCOUNT_INSUFFICIENT_FUNDS_ERROR.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testGivenInsufficientCreditCardFundsWhenExecuteThenThrowInsufficientFundsException() throws TMBCommonException, TMBCommonExceptionWithResponse {
        cacheData = initializeCacheDataForAutoLoan();
        request = initializeDefaultRequest();

        setupBasicMocks();
        setupCreditCardPayment();
        mockGetCreditCardAccountWithAccountId();
        mockValidateAutoLoanPayment();
        mockStaticTransactionGenerateId();
        mockDeepLinkWithAmount(DEFAULT_PAYMENT_AMOUNT.toString(), DEFAULT_PAYMENT_AMOUNT.doubleValue());
        request.getCreditCard().setAmount(BigDecimal.valueOf(***********.99));
        doThrow(new TMBCommonException(ResponseCode.CREDIT_CARD_INSUFFICIENT_FUNDS_ERROR.getCode(), ResponseCode.CREDIT_CARD_INSUFFICIENT_FUNDS_ERROR.getMessage(), ResponseCode.CREDIT_CARD_INSUFFICIENT_FUNDS_ERROR.getService(), HttpStatus.OK, null)).when(baseBillPayValidator).validateInsufficientFund(any(), any(), any(), any());

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> autoLoanValidationProcessor.executeValidate(request, headers, cacheData));

        assertEquals(ResponseCode.CREDIT_CARD_INSUFFICIENT_FUNDS_ERROR.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testGivenDailyLimitExceededWhenExecuteThenThrowDailyLimitExceededException() throws TMBCommonException {
        cacheData = initializeCacheDataForAutoLoan();
        cacheData.getPaymentInformation().getProductDetail().setProductRef1("not own");
        request = initializeDefaultRequest();
        setupBasicMocks();

        mockGetAccountByAccountNumber();
        mockDailyLimitExceeded();

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> autoLoanValidationProcessor.executeValidate(request, headers, cacheData));

        assertEquals(ResponseCode.DAILY_LIMIT_EXCEEDED.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testGivenNullDeepLinkWhenExecuteThenNotThrowException() throws TMBCommonException, JsonProcessingException, TMBCommonExceptionWithResponse {
        cacheData = initializeCacheDataForAutoLoan();
        request = initializeDefaultRequest();

        setupForSuccessCase();
        mockGetAccountByAccountNumber();
        mockStaticTransactionGenerateId();
        mockDeepLinkWithAmount(DEFAULT_PAYMENT_AMOUNT.toString(), DEFAULT_PAYMENT_AMOUNT.doubleValue());

        assertDoesNotThrow(() -> autoLoanValidationProcessor.executeValidate(request, headers, cacheData));
    }

    @Test
    void testGivenMatchingDeepLinkAmountWhenExecuteThenNotThrowException() throws TMBCommonException, TMBCommonExceptionWithResponse {
        cacheData = initializeCacheDataForAutoLoan();
        initialDeepLinkRequestInCache();
        request = initializeDefaultRequest();
        setupBasicMocks();
        mockGetAccountByAccountNumber();
        mockDeepLinkWithAmount("9500.50", DEFAULT_PAYMENT_AMOUNT.doubleValue());
        mockValidateAutoLoanPayment();
        mockValidateIsRequireCommonAuthForBillReturnRequireCommonAuth();
        mockStaticTransactionGenerateId();

        try (MockedStatic<TMBUtils> mockedTMBUtils = mockStatic(TMBUtils.class);
             MockedStatic<ServiceHoursUtils> mockedServiceHoursUtils = mockServiceHours(true)) {

            AutoLoanPaymentDetail paymentDetail = new AutoLoanPaymentDetail();
            paymentDetail.setDebtFeetotalAmt(DEFAULT_PAYMENT_AMOUNT.doubleValue());
            mockedTMBUtils.when(() -> TMBUtils.convertStringToJavaObjWithTypeReference(anyString(), any(TypeReference.class)))
                    .thenReturn(paymentDetail);

            setupTMBUtilsMock(mockedTMBUtils);

            assertDoesNotThrow(() -> autoLoanValidationProcessor.executeValidate(request, headers, cacheData));
        }
    }

    @Test
    void testGivenMismatchDeepLinkAmountWhenExecuteThenThrowException() throws TMBCommonException {
        cacheData = initializeCacheDataForAutoLoan();
        initialDeepLinkRequestInCache();
        request = initializeDefaultRequest();
        setupBasicMocks();

        mockGetAccountByAccountNumber();
        mockDeepLinkWithAmount("9500.50", 8500.50);

        try (MockedStatic<TMBUtils> mockedTMBUtils = mockStatic(TMBUtils.class);
             MockedStatic<ServiceHoursUtils> mockedServiceHoursUtils = mockServiceHours(true)) {

            setupTMBUtilsMock(mockedTMBUtils);
            AutoLoanPaymentDetail paymentDetail = new AutoLoanPaymentDetail();
            paymentDetail.setDebtFeetotalAmt(8500.50);
            mockedTMBUtils.when(() -> TMBUtils.convertStringToJavaObjWithTypeReference(anyString(), any(TypeReference.class)))
                    .thenReturn(paymentDetail);

            TMBCommonException exception = assertThrows(TMBCommonException.class,
                    () -> autoLoanValidationProcessor.executeValidate(request, headers, cacheData));

            assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
        }
    }

    @Test
    void testGivenNonAutoLoanDeepLinkWhenExecuteThenSkipValidation() throws TMBCommonException, TMBCommonExceptionWithResponse {
        cacheData = initializeCacheDataForAutoLoan();
        request = initializeDefaultRequest();
        setupBasicMocks();

        DeepLinkRequest deepLinkRequest = new DeepLinkRequest();
        deepLinkRequest.setCallFrom("someOtherSource");

        mockDeepLinkWithAmount(DEFAULT_PAYMENT_AMOUNT.toString(), DEFAULT_PAYMENT_AMOUNT.doubleValue());
        mockValidateAutoLoanPayment();
        mockValidateIsRequireCommonAuthForBillReturnRequireCommonAuth();
        mockGetAccountByAccountNumber();
        mockStaticTransactionGenerateId();

        ValidationCommonPaymentResponse response = autoLoanValidationProcessor.executeValidate(request, headers, cacheData);

        assertNotNull(response);
    }

    @Test
    void testGivenNonRequiredTransactionTypeWhenExecuteThenSkipAmountValidation() throws TMBCommonException, TMBCommonExceptionWithResponse {
        cacheData = initializeCacheDataForAutoLoan();
        request = initializeDefaultRequest();
        setupBasicMocks();

        DeepLinkRequest deepLinkRequest = new DeepLinkRequest();
        deepLinkRequest.setCallFrom("autoLoan");
        deepLinkRequest.setTransType("03");
        deepLinkRequest.setRef1(DEFAULT_DEPOSIT_ACCOUNT);
        deepLinkRequest.setAmount(DEFAULT_PAYMENT_AMOUNT.toString());

        mockGetAccountByAccountNumber();
        mockValidateAutoLoanPayment();
        mockValidateIsRequireCommonAuthForBillReturnRequireCommonAuth();
        mockStaticTransactionGenerateId();
        mockDeepLinkWithAmount(DEFAULT_PAYMENT_AMOUNT.toString(), DEFAULT_PAYMENT_AMOUNT.doubleValue());

        ValidationCommonPaymentResponse response = autoLoanValidationProcessor.executeValidate(request, headers, cacheData);

        assertNotNull(response);
    }

    @Test
    void testGivenAldxFeeCacheErrorWhenExecuteThenHandleException() throws TMBCommonException {
        cacheData = initializeCacheDataForAutoLoan();
        initialDeepLinkRequestInCache();
        request = initializeDefaultRequest();

        setupBasicMocks();
        mockGetAccountByAccountNumber();

        DeepLinkRequest deepLinkRequest = new DeepLinkRequest();
        deepLinkRequest.setCallFrom("autoLoan");
        deepLinkRequest.setTransType("01");
        deepLinkRequest.setRef1(DEFAULT_DEPOSIT_ACCOUNT);
        deepLinkRequest.setAmount(DEFAULT_PAYMENT_AMOUNT.toString());

        TMBCommonException serviceTmbException = CommonServiceUtils.getUnhandledTmbCommonException(
                ResponseCode.FAILED_V2, "Service unavailable");
        when(hpExpService.getAldxFeeCache(anyString(), anyString(), anyString(), anyString(), anyString(), anyString()))
                .thenThrow(serviceTmbException);


        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> autoLoanValidationProcessor.executeValidate(request, headers, cacheData));

        assertEquals(serviceTmbException.getErrorCode(), exception.getErrorCode());
    }

    @Test
    void testGivenPreLoginTrueWhenExecuteThenSkipValidation() throws TMBCommonException, TMBCommonExceptionWithResponse {
        cacheData = initializeCacheDataForAutoLoan();
        request = initializeDefaultRequest();

        setupBasicMocks();
        mockGetAccountByAccountNumber();

        HttpHeaders preLoginHeaders = new HttpHeaders();
        preLoginHeaders.add(HEADER_CRM_ID, crmId);
        preLoginHeaders.add(HEADER_CORRELATION_ID, correlationId);
        preLoginHeaders.add(HEADER_ACCEPT_LANGUAGE, acceptLanguage);
        preLoginHeaders.add(HEADER_APP_VERSION, appVersion);
        preLoginHeaders.add(HEADER_PRE_LOGIN, "true");

        DeepLinkRequest deepLinkRequest = new DeepLinkRequest();
        deepLinkRequest.setCallFrom("autoLoan");
        deepLinkRequest.setTransType("01");
        deepLinkRequest.setRef1(DEFAULT_DEPOSIT_ACCOUNT);
        deepLinkRequest.setAmount(DEFAULT_PAYMENT_AMOUNT.toString());

        mockValidateAutoLoanPayment();
        mockValidateIsRequireCommonAuthForBillReturnRequireCommonAuth();
        mockStaticTransactionGenerateId();
        initialDeepLinkRequestInCache();

        ValidationCommonPaymentResponse response = autoLoanValidationProcessor.executeValidate(request, preLoginHeaders, cacheData);

        assertNotNull(response);
    }

    @Test
    void testGivenExecutionExceptionWhenExecuteThenThrowTMBCommonException() throws TMBCommonException {
        cacheData = initializeCacheDataForAutoLoan();
        request = initializeDefaultRequest();

        doThrow(new RuntimeException("Test exception")).when(asyncHelper).executeMethodAsync(any());

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> autoLoanValidationProcessor.executeValidate(request, headers, cacheData));

        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
    }

    @Test
    void testGivenJsonProcessingExceptionWhenExecuteThenThrowTMBCommonException() throws TMBCommonException {
        cacheData = initializeCacheDataForAutoLoan();
        initialDeepLinkRequestInCache();
        request = initializeDefaultRequest();

        setupBasicMocks();
        mockGetAccountByAccountNumber();

        try (MockedStatic<TMBUtils> mockedTMBUtils = mockStatic(TMBUtils.class);
             MockedStatic<ServiceHoursUtils> mockedServiceHoursUtils = mockServiceHours(true)) {

            mockedTMBUtils.when(() -> TMBUtils.convertStringToJavaObjWithTypeReference(anyString(), any()))
                    .thenThrow(new JsonProcessingException("Invalid JSON") {
                    });

            TMBCommonException exception = assertThrows(TMBCommonException.class,
                    () -> autoLoanValidationProcessor.executeValidate(request, headers, cacheData));

            assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
        }
    }

    @Test
    void testGivenMismatchRef2WhenValidateRef2ThenThrowException() {
        TopUpETETransaction transaction = new TopUpETETransaction();
        AutoLoanTBankCustomer tBankCustomer = new AutoLoanTBankCustomer();
        tBankCustomer.setCustomerNumber("123456");
        transaction.setTbankCustomer(tBankCustomer);
        String ref2 = "654321";

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> autoLoanValidationProcessor.validateRef2(ref2, transaction));

        assertEquals(ResponseCode.AL_REF2_MISS_MATCH_ERROR.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
    }

    @Test
    void testGivenNullTBankCustomerWhenValidateRef2ThenThrowException() {
        TopUpETETransaction transaction = new TopUpETETransaction();
        String ref2 = "654321";

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> autoLoanValidationProcessor.validateRef2(ref2, transaction));

        assertEquals(ResponseCode.AL_REF2_MISSING.getCode(), exception.getErrorCode());
    }

    @ParameterizedTest
    @CsvSource({
            "25.00, 1, true, 0.00",
            "25.00, 0, false, 25.00"
    })
    void testGivenVariousParametersWhenGetFeeThenReturnExpectedFee(
            String feeAmount, String waiveFee, boolean isPayWithCreditCard, String expectedFee) {
        TopUpETETransaction transaction = new TopUpETETransaction();
        transaction.setFee(new com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpFee());
        transaction.getFee().setBillPayment(new BigDecimal(feeAmount));

        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setWaiveFeeForBillpay(waiveFee);

        BigDecimal actualFee = autoLoanValidationProcessor.getFee(transaction, isPayWithCreditCard, depositAccount);

        assertEquals(new BigDecimal(expectedFee), actualFee);
    }

    @Test
    void testGivenCreditCardPaymentWhenGetAmountThenReturnCreditCardAmount() {
        ValidationCommonPaymentRequest request = new ValidationCommonPaymentRequest();
        CreditCardValidationCommonPaymentRequest creditCard = new CreditCardValidationCommonPaymentRequest();
        creditCard.setPayWithCreditCardFlag(true);
        creditCard.setAmount(BigDecimal.valueOf(1000.00));
        request.setCreditCard(creditCard);

        BigDecimal result = autoLoanValidationProcessor.getAmount(request);

        assertEquals(BigDecimal.valueOf(1000.00), result);
    }

    @Test
    void testGivenDepositPaymentWhenGetAmountThenReturnDepositAmount() {
        ValidationCommonPaymentRequest request = new ValidationCommonPaymentRequest();
        DepositValidationCommonPaymentRequest deposit = new DepositValidationCommonPaymentRequest();
        deposit.setAmount(BigDecimal.valueOf(2000.00));
        request.setDeposit(deposit);

        CreditCardValidationCommonPaymentRequest creditCard = new CreditCardValidationCommonPaymentRequest();
        creditCard.setPayWithCreditCardFlag(false);
        request.setCreditCard(creditCard);

        BigDecimal result = autoLoanValidationProcessor.getAmount(request);

        assertEquals(BigDecimal.valueOf(2000.00), result);
    }

    @Test
    void testGivenTMBCommonExceptionWhenHandleExceptionThenReturnOriginalException() {
        TMBCommonException originalException = new TMBCommonException(
                ResponseCode.BILLER_EXPIRED.getCode(),
                ResponseCode.BILLER_EXPIRED.getMessage(),
                ResponseCode.BILLER_EXPIRED.getService(),
                HttpStatus.BAD_REQUEST,
                null);

        TMBCommonException result = assertThrows(TMBCommonException.class,
                () -> autoLoanValidationProcessor.handleException(
                        request, headers, cacheData, null, null, null, originalException));

        assertEquals(originalException, result);
    }

    @Test
    void testGivenRuntimeExceptionWhenHandleExceptionThenReturnFailedV2Exception() throws TMBCommonException, TMBCommonExceptionWithResponse {
        request = initializeDefaultRequest();
        RuntimeException runtimeException = new RuntimeException("Test runtime exception");

        TMBCommonException result = autoLoanValidationProcessor.handleException(request, headers, cacheData, null, null, null, runtimeException);

        assertEquals(ResponseCode.FAILED_V2.getCode(), result.getErrorCode());
    }

    @Test
    void testExecuteAutoLoanWhenFailedTMBCommonExceptionAtPrePareDateExecuteRequestAsyncShouldThrowTMBCommonException() throws TMBCommonException, JsonProcessingException {
        cacheData = initializeCacheDataForAutoLoan();
        request = initializeDefaultRequest();
        setupErrorMocks();

        TMBCommonException expectedException = new TMBCommonException(ResponseCode.FAILED_V2.getCode(), "message", "service", HttpStatus.INTERNAL_SERVER_ERROR, null);
        CompletableFuture<Object> failedFuture = CompletableFuture.failedFuture(expectedException);
        doReturn(failedFuture).when(asyncHelper).executeRequestAsync(any());

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> autoLoanValidationProcessor.executeValidate(request, headers, cacheData));

        assertEquals(expectedException, exception);
    }

    @Test
    void testExecuteAutoLoanWhenFailedUnHandleExceptionAtPrePareDateExecuteRequestAsyncShouldThrowTMBCommonException() throws TMBCommonException {
        cacheData = initializeCacheDataForAutoLoan();
        request = initializeDefaultRequest();
        setupErrorMocks();

        TMBCommonException expectedException = new TMBCommonException(ResponseCode.FAILED_V2.getCode(), "message", "service", HttpStatus.INTERNAL_SERVER_ERROR, null);
        CompletableFuture<Object> failedFuture = CompletableFuture.failedFuture(new RuntimeException("Test runtime exception"));
        doReturn(failedFuture).when(asyncHelper).executeRequestAsync(any());

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> autoLoanValidationProcessor.executeValidate(request, headers, cacheData));

        assertEquals(expectedException.getErrorCode(), exception.getErrorCode());
    }

    @Test
    void testValidateDeepLinkRequestWhenTransTypeNotRequiredForAmountCheckShouldReturnImmediately() {
        String notRequiredTransType = "99";
        DeepLinkRequestInCache deepLinkRequest = new DeepLinkRequestInCache();
        deepLinkRequest.setTransType(notRequiredTransType);
        deepLinkRequest.setCallFrom(BILLER_CALL_FROM_AUTO_LOAN);
        cacheData = initializeCacheDataForAutoLoan();
        cacheData.setDeepLinkRequest(deepLinkRequest);

        HttpHeaders headers = new HttpHeaders();
        headers.add(HEADER_CRM_ID, "crmId");
        headers.add(HEADER_CORRELATION_ID, "correlationId");
        headers.add(HEADER_ACCEPT_LANGUAGE, "th");
        headers.add(HEADER_APP_VERSION, "5.12.0");

        assertDoesNotThrow(() -> TestUtils.invokeMethod(autoLoanValidationProcessor, "validateDeepLinkRequest", deepLinkRequest, headers, "crmId", cacheData));
    }

    @Test
    void testValidateDeepLinkRequestWhenJsonProcessingExceptionThenThrowTMBCommonException() throws Exception {
        DeepLinkRequestInCache deepLinkRequest = new DeepLinkRequestInCache();
        deepLinkRequest.setCallFrom(BILLER_CALL_FROM_AUTO_LOAN);
        deepLinkRequest.setTransType("01");

        CommonPaymentDraftCache cache = initializeCacheDataForAutoLoan();
        cache.setDeepLinkRequest(deepLinkRequest);

        HttpHeaders headers = new HttpHeaders();
        headers.add(HEADER_CRM_ID, "crmId");
        headers.add(HEADER_CORRELATION_ID, "correlationId");
        headers.add(HEADER_ACCEPT_LANGUAGE, "th");
        headers.add(HEADER_APP_VERSION, "5.12.0");
        headers.add(HEADER_PRE_LOGIN, "false");

        CacheResponse cacheResponse = new CacheResponse();
        cacheResponse.setValue("{\"invalidJson\": }");
        when(hpExpService.getAldxFeeCache(anyString(), anyString(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn(cacheResponse);


        try (MockedStatic<TMBUtils> mockedTMBUtils = mockStatic(TMBUtils.class)) {
            mockedTMBUtils.when(() -> TMBUtils.convertStringToJavaObjWithTypeReference(anyString(), any(TypeReference.class)))
                    .thenThrow(new JsonProcessingException("Invalid JSON") {
                    });

            TMBCommonException exception = assertThrows(TMBCommonException.class,
                    () -> TestUtils.invokeMethodWithThrow(autoLoanValidationProcessor, "validateDeepLinkRequest", deepLinkRequest, headers, "crmId", cache));

            assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
            assertEquals("Payment amount is incorrect format", exception.getErrorMessage());
        }
    }

    @Test
    void testIsPayBillBySelfTTBAutoLoanWhenReference1IsNullShouldReturnFalse() {
        List<LoanAccount> loanAccounts = new ArrayList<>();
        loanAccounts.add(new LoanAccount());

        boolean result = TestUtils.invokeMethod(autoLoanValidationProcessor, "isPayBillBySelfTTBAutoLoan", loanAccounts, null);

        assertFalse(result);
    }

    @Test
    void testIsPayBillBySelfTTBAutoLoanWhenLoanAccountsIsNullShouldReturnFalse() {
        String reference1 = "***********";

        boolean result = TestUtils.invokeMethod(autoLoanValidationProcessor, "isPayBillBySelfTTBAutoLoan", null, reference1);

        assertFalse(result);
    }

    @Test
    void testIsPayBillBySelfTTBAutoLoanWhenLoanAccountsIsEmptyShouldReturnFalse() {
        List<LoanAccount> loanAccounts = new ArrayList<>();
        String reference1 = "***********";

        boolean result = TestUtils.invokeMethod(autoLoanValidationProcessor, "isPayBillBySelfTTBAutoLoan", loanAccounts, reference1);

        assertFalse(result);
    }

    @Test
    void testIsPayBillBySelfTTBAutoLoanWhenReference1IsTooShortShouldReturnFalse() {
        List<LoanAccount> loanAccounts = new ArrayList<>();
        loanAccounts.add(new LoanAccount());
        String reference1 = "12345";

        boolean result = TestUtils.invokeMethod(autoLoanValidationProcessor, "isPayBillBySelfTTBAutoLoan", loanAccounts, reference1);

        assertFalse(result);
    }

    @Test
    void testIsPayBillBySelfTTBAutoLoanWhenReference1IsValidButNoMatchShouldReturnFalse() {
        List<LoanAccount> loanAccounts = new ArrayList<>();
        LoanAccount account1 = new LoanAccount();
        account1.setAccountNumber("*********");
        loanAccounts.add(account1);

        String reference1 = "A23BCDEFG01";

        boolean result = TestUtils.invokeMethod(autoLoanValidationProcessor, "isPayBillBySelfTTBAutoLoan", loanAccounts, reference1);

        assertFalse(result);
    }

    @Test
    void testIsPayBillBySelfTTBAutoLoanWhenReference1IsValidAndMatchShouldReturnTrue() {
        List<LoanAccount> loanAccounts = new ArrayList<>();
        LoanAccount account1 = new LoanAccount();
        account1.setAccountNumber("23CDEFG01");
        loanAccounts.add(account1);
        LoanAccount account2 = new LoanAccount();
        account2.setAccountNumber("*********");
        loanAccounts.add(account2);

        String reference1 = "A23BCDEFG01";

        boolean result = TestUtils.invokeMethod(autoLoanValidationProcessor, "isPayBillBySelfTTBAutoLoan", loanAccounts, reference1);

        assertTrue(result);
    }

    @Nested
    class HandleExceptionTest {
        @Test
        void testHandleException_WhenFailedTMBCommonException_ShouldThrowTMBCommonException() {
            assertThrows(TMBCommonException.class, () -> autoLoanValidationProcessor.handleException(null, null, null, null, null, null, new TMBCommonException("ignore message")));
        }

        @Test
        void testHandleException_WhenFailedTMBCommonExceptionWithResponse_ShouldThrowTMBCommonExceptionWithResponse() {
            assertThrows(TMBCommonExceptionWithResponse.class, () -> autoLoanValidationProcessor.handleException(null, null, null, null, null, null, new TMBCommonExceptionWithResponse("", "", "", HttpStatus.OK, null, null)));
        }
    }

    private MockedStatic<ServiceHoursUtils> mockServiceHours(boolean isDuringServiceHours) {
        MockedStatic<ServiceHoursUtils> mockedServiceHoursUtils = mockStatic(ServiceHoursUtils.class);
        mockedServiceHoursUtils.when(() -> ServiceHoursUtils.isDuringServiceHours(any(), any()))
                .thenReturn(isDuringServiceHours);
        return mockedServiceHoursUtils;
    }

    private void setupForSuccessCase() throws TMBCommonException, TMBCommonExceptionWithResponse {
        cacheData = initializeCacheDataForAutoLoan();
        request = initializeDefaultRequest();

        setupBasicMocks();
        mockValidateAutoLoanPayment();
        mockValidateIsRequireCommonAuthForBillReturnRequireCommonAuth();
    }

    private void setupBasicMocks() throws TMBCommonException {
        setUpAsyncHelperExecuteRequestAsync();
        setUpAsyncHelperExecuteMethodAsync();
        mockFetchBillPayConfig();
        mockFetchMasterBiller();
        mockFetchCustomerKYC();
        mockFetchCustomerCrmProfile();
        mockFetchHpAccountList();
    }

    private void setupErrorMocks() throws TMBCommonException {
        setUpAsyncHelperExecuteMethodAsync();
        mockFetchBillPayConfig();
        mockFetchMasterBiller();
        mockFetchCustomerKYC();
        mockFetchHpAccountList();
    }

    private void setupCreditCardPayment() {
        request.getDeposit().setPayWithDepositFlag(false).setAmount(null).setAccountNumber(null);
        request.setCreditCard(
                new CreditCardValidationCommonPaymentRequest()
                        .setPayWithCreditCardFlag(true)
                        .setAmount(DEFAULT_PAYMENT_AMOUNT)
                        .setAccountId(DEFAULT_CREDIT_CARD_ID)
        );
        cacheData.setValidateRequest(request);
    }

    private void mockExpiredBiller() throws TMBCommonException {
        doThrow(CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.BILLER_EXPIRED)).when(baseBillPayValidator).validateBillerExpiration(any(MasterBillerResponse.class));
    }

    private void mockDailyLimitExceeded() throws TMBCommonException {
        TMBCommonException dailyLimitException = CommonServiceUtils.getBusinessTmbCommonException(
                ResponseCode.DAILY_LIMIT_EXCEEDED);
        doThrow(dailyLimitException).when(dailyLimitService)
                .validateDailyLimitExceeded(anyString(), any(CustomerCrmProfile.class), any(BigDecimal.class));
    }

    private void mockDeepLinkWithAmount(String requestAmount, double cacheAmount) throws TMBCommonException {
        DeepLinkRequestInCache deepLinkRequest = new DeepLinkRequestInCache();
        deepLinkRequest.setCallFrom("autoLoan");
        deepLinkRequest.setTransType("01");
        deepLinkRequest.setAmount(requestAmount);

        CacheResponse cacheResponse = new CacheResponse();
        cacheResponse.setValue("{\"debt_FeetotalAmt\":" + cacheAmount + "}");
        when(hpExpService.getAldxFeeCache(anyString(), anyString(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn(cacheResponse);
        cacheData.setDeepLinkRequest(deepLinkRequest);
    }

    private void setupTMBUtilsMock(MockedStatic<TMBUtils> mockedTMBUtils) {
        mockedTMBUtils.when(() -> TMBUtils.convertStringToJavaObj(anyString(), any())).thenCallRealMethod();
        mockedTMBUtils.when(() -> TMBUtils.convertJavaObjectToString(any())).thenCallRealMethod();
    }

    private void assertSuccessfulResponse(ValidationCommonPaymentResponse response) {
        assertNotNull(response.getFee());
        assertNotNull(response.getTransactionId());
        assertNotNull(response.getTotalAmount());
        assertTrue(response.getIsRequireCommonAuthen());
        assertEquals(COMMON_PAYMENT_BILL_PAY_FEATURE_ID, response.getCommonAuthenticationInformation().getFeatureId());
        assertEquals(COMMON_AUTH_BILL_FLOW_NAME, response.getCommonAuthenticationInformation().getFlowName());
        assertNotNull(response.getCommonAuthenticationInformation().getTotalPaymentAccumulateUsage());
    }

    private void assertShouldNotValidateDailyLimit() throws TMBCommonException {
        Mockito.verify(dailyLimitService, never()).validateDailyLimitExceeded(any(), any(), any());
    }

    private CommonPaymentDraftCache initializeCacheDataForAutoLoan() {
        PaymentInformation paymentInfo = new PaymentInformation();
        paymentInfo.setCompCode(BILL_COMP_CODE_TTB_DRIVE_CAR_LOAN);
        paymentInfo.setProductDetail(new ProductDetail().setProductRef1(DEFAULT_REF1).setProductRef2(DEFAULT_REF2));

        AmountDetail amountDetail = new AmountDetail();
        amountDetail.setAmountValue(DEFAULT_PAYMENT_AMOUNT);
        amountDetail.setAmountLabelTh("Amount label th");
        amountDetail.setAmountLabelEn("Amount label en");
        paymentInfo.setAmountDetail(amountDetail);

        CommonPaymentDraftCache cache = new CommonPaymentDraftCache();
        ValidationCommonPaymentRequest request = new ValidationCommonPaymentRequest();
        cache.setPaymentInformation(paymentInfo);
        cache.setCrmId(crmId);
        cache.setValidateRequest(request);

        ValidationCommonPaymentDraftCache validateDraftCache = new ValidationCommonPaymentDraftCache();
        cache.setValidateDraftCache(validateDraftCache);
        return cache;
    }

    private ValidationCommonPaymentRequest initializeDefaultRequest() {
        ValidationCommonPaymentRequest req = new ValidationCommonPaymentRequest();

        DepositValidationCommonPaymentRequest deposit = new DepositValidationCommonPaymentRequest();
        deposit.setPayWithDepositFlag(true);
        deposit.setAccountNumber(depositAccountNumber);
        deposit.setAmount(DEFAULT_PAYMENT_AMOUNT);
        req.setDeposit(deposit);

        CreditCardValidationCommonPaymentRequest creditCard = new CreditCardValidationCommonPaymentRequest();
        creditCard.setPayWithCreditCardFlag(false);
        req.setCreditCard(creditCard);

        req.setTransactionId(DEFAULT_TRANSACTION_ID);
        return req;
    }

    private void setUpAsyncHelperExecuteRequestAsync() throws TMBCommonException {
        when(asyncHelper.executeRequestAsync(any())).thenAnswer(invocation -> {
            Supplier<ResponseEntity<TmbServiceResponse<?>>> supplier = invocation.getArgument(0);
            ResponseEntity<TmbServiceResponse<?>> feignResponseEntity = supplier.get();
            return CompletableFuture.completedFuture(Objects.requireNonNull(feignResponseEntity.getBody()).getData());
        });
    }

    private void setUpAsyncHelperExecuteMethodAsync() throws TMBCommonException {
        when(asyncHelper.executeMethodAsync(any())).thenAnswer(invocation -> {
            ThrowableSupplier<?> supplier = invocation.getArgument(0);
            return CompletableFuture.completedFuture(supplier.get());
        });
    }

    private void mockFetchBillPayConfig() throws TMBCommonException {
        BillPayConfiguration billPayConfig = new BillPayConfiguration();
        when(paymentService.getBillPayConfig(correlationId)).thenReturn(billPayConfig);
    }

    private void mockFetchMasterBiller() throws TMBCommonException {
        MasterBillerResponse masterBiller = new MasterBillerResponse();
        BillerInfoResponse billerInfo = new BillerInfoResponse();
        billerInfo.setExpiredDate("9999-12-31T00:00:00.000000+07:00");
        billerInfo.setStartTime("09:00");
        billerInfo.setEndTime("17:00");
        billerInfo.setToAccountId(DEFAULT_DEPOSIT_ACCOUNT);
        billerInfo.setBillerCategoryCode(BILLER_CATEGORY_CODE_CREDIT_CARD);
        billerInfo.setBillerGroupType(BILLER_GROUP_TYPE_BILL);
        masterBiller.setBillerInfo(billerInfo);
        masterBiller.setRef1(new ReferenceResponse());

        when(paymentService.getMasterBiller(anyString(), anyString())).thenReturn(masterBiller);
    }

    private void mockFetchCustomerKYC() throws TMBCommonException {
        CustomerKYCResponse customerKYC = new CustomerKYCResponse();
        customerKYC.setIdNo("*************");
        customerKYC.setCustomerFirstNameEn("John");
        customerKYC.setCustomerLastNameEn("Doe");

        when(customerService.getCustomerKYC(anyString(), anyString())).thenReturn(customerKYC);
    }

    private void mockFetchCustomerCrmProfile() {
        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
        customerCrmProfile.setPaymentAccuUsgAmt(BigDecimal.valueOf(100.50));
        customerCrmProfile.setBillpayAccuUsgAmt(BigDecimal.valueOf(2000.50));

        TmbServiceResponse<CustomerCrmProfile> response = new TmbServiceResponse<>();
        response.setStatus(CommonServiceUtils.getStatusSuccess());
        response.setData(customerCrmProfile);

        when(customerServiceClient.fetchCustomerCrmProfile(anyString(), anyString())).thenReturn(ResponseEntity.ok(response));
    }

    private void mockGetAccountByAccountNumber() throws TMBCommonException {
        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setAccountNumber(depositAccountNumber);
        depositAccount.setAccountType("SDA");
        depositAccount.setAvailableBalance(BigDecimal.valueOf(10000.00));
        depositAccount.setWaiveFeeForBillpay("0");

        when(billPayAccountCommonPaymentService.getAccountByAccountNumber(anyString(), any(HttpHeaders.class))).thenReturn(depositAccount);
    }

    private void mockGetCreditCardAccountWithAccountId() throws TMBCommonException {
        CreditCardSupplementary creditCard = new CreditCardSupplementary();
        creditCard.setAccountId(DEFAULT_CREDIT_CARD_ID);
        creditCard.setCardNo("****************");

        CardBalancesAsync cardBalances = new CardBalancesAsync();
        cardBalances.setAvailableCreditAllowance(BigDecimal.valueOf(10000.00));
        creditCard.setCardBalances(cardBalances);

        when(accountCreditCardService.getCreditCardAccountWithAccountId(anyString(), anyString(), anyString())).thenReturn(creditCard);
    }

    private void mockFetchHpAccountList() throws TMBCommonException {
        List<LoanAccount> hpAccounts = new ArrayList<>();
        LoanAccount hpAccount = new LoanAccount();
        hpAccount.setAccountNumber("*********");
        hpAccounts.add(hpAccount);
        when(accountCommonPaymentHelper.fetchHpAccountList(anyString(), anyString())).thenReturn(hpAccounts);
    }

    private void mockValidateAutoLoanPayment() throws TMBCommonException, TMBCommonExceptionWithResponse {
        TopUpETEResponse response = new TopUpETEResponse();
        TopUpETETransaction transaction = new TopUpETETransaction();
        transaction.setAmount(DEFAULT_PAYMENT_AMOUNT);

        com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpFee fee = new com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpFee();
        fee.setBillPayment(BigDecimal.valueOf(0.00));
        transaction.setFee(fee);

        AutoLoanTBankCustomer tBankCustomer = new AutoLoanTBankCustomer();
        tBankCustomer.setName("John Doe");
        tBankCustomer.setCustomerNumber(DEFAULT_REF2);
        transaction.setTbankCustomer(tBankCustomer);

        response.setTransaction(transaction);

        when(paymentService.validateAutoLoanPayment(anyString(), anyString(), any(TopUpETEPaymentRequest.class))).thenReturn(response);
    }

    private void mockValidateIsRequireCommonAuthForBillReturnRequireCommonAuth() throws TMBCommonException {
        CommonAuthenResult result = new CommonAuthenResult();
        result.setRequireCommonAuthen(true);

        when(dailyLimitPinFreeValidator.validateIsRequireCommonAuthForBill(
                any(HttpHeaders.class), any(BigDecimal.class), anyBoolean(), any(CustomerCrmProfile.class)))
                .thenReturn(result);
    }

    private void mockStaticTransactionGenerateId() {
        when(Transaction.getTransactionId(anyString(), anyInt())).thenReturn("transaction-after-generate");
    }

    private void initialDeepLinkRequestInCache() {
        DeepLinkRequestInCache deepLinkRequest = new DeepLinkRequestInCache();
        deepLinkRequest.setCallFrom("autoLoan");
        deepLinkRequest.setTransType("01");
        deepLinkRequest.setAmount(DEFAULT_PAYMENT_AMOUNT.toString());

        cacheData.setDeepLinkRequest(deepLinkRequest);
    }
}
