package com.tmb.oneapp.commonpaymentexp.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.UtilityClass;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.SecureRandom;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

@UtilityClass
public class AsymmetricKeyUtility {

    private static final TMBLogger<AsymmetricKeyUtility> logger = new TMBLogger<>(AsymmetricKeyUtility.class);
    /**
     * Specifies the algorithm for RSA encryption.
     * - RSA: The core algorithm.
     * - ECB (Electronic Codebook): The mode of operation. Simple, but less secure for repetitive data. Suitable here as we only encrypt a random, unique AES key.
     * - PKCS1Padding: The padding scheme, which is standard for RSA.
     */
    private static final String RSA_ALGORITHM = "RSA/ECB/PKCS1Padding";
    /**
     * Specifies the algorithm for AES symmetric encryption.
     * - AES: The core algorithm.
     * - GCM (Galois/Counter Mode): An authenticated encryption mode that provides both confidentiality and data integrity.
     * - NoPadding: GCM is a stream cipher mode and does not require padding.
     */
    private static final String AES_ALGORITHM = "AES/GCM/NoPadding";
    /**
     * The key size in bits for the AES symmetric key. 256 bits is a strong standard.
     */
    private static final int AES_KEY_SIZE = 256;
    /**
     * The length in bytes of the Initialization Vector (IV) for GCM mode. 12 bytes (96 bits) is recommended by NIST for performance and security.
     */
    private static final int GCM_IV_LENGTH = 12; // 96 bits
    /**
     * The length in bytes of the GCM authentication tag. 16 bytes (128 bits) provides a high level of assurance against forgery.
     */
    private static final int GCM_TAG_LENGTH = 16; // 128 bits

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HybridEncryptedData {
        private String encryptedKey; // AES key encrypted with RSA
        private String encryptedPayload; // Data encrypted with AES
        private String iv; // Initialization Vector for GCM
    }

    public String encrypt(String plainText, String publicKeyStr) throws TMBCommonException {
        try {
            // --- ขั้นตอนที่ 1: สร้างกุญแจ AES (Symmetric Key) แบบสุ่มขึ้นมาใหม่ทุกครั้ง ---
            // สร้างตัวสร้างกุญแจสำหรับอัลกอริทึม AES
            KeyGenerator keyGen = KeyGenerator.getInstance("AES");
            // กำหนดขนาดของกุญแจ AES ที่จะสร้าง (256 บิต)
            keyGen.init(AES_KEY_SIZE);
            // สร้างและรับ SecretKey ของ AES
            SecretKey aesKey = keyGen.generateKey();

            // --- ขั้นตอนที่ 2: เข้ารหัส AES Key ด้วย RSA Public Key ---
            // โหลด RSA Public Key จาก String ที่ได้รับมา
            PublicKey rsaPublicKey = loadPublicKey(publicKeyStr);
            // สร้าง Cipher สำหรับการเข้ารหัสด้วย RSA
            Cipher rsaCipher = Cipher.getInstance(RSA_ALGORITHM);
            // เริ่มต้น Cipher ในโหมดเข้ารหัส (ENCRYPT_MODE) โดยใช้ RSA Public Key
            rsaCipher.init(Cipher.ENCRYPT_MODE, rsaPublicKey);
            // ทำการเข้ารหัส byte ของ AES Key
            byte[] encryptedAesKeyBytes = rsaCipher.doFinal(aesKey.getEncoded());
            // แปลงผลลัพธ์การเข้ารหัส AES Key เป็น Base64 String เพื่อให้ง่ายต่อการส่ง
            String encryptedAesKeyBase64 = Base64.getEncoder().encodeToString(encryptedAesKeyBytes);

            // --- ขั้นตอนที่ 3: เข้ารหัสข้อมูลจริง (Payload) ด้วย AES Key ---
            // สร้าง Initialization Vector (IV) แบบสุ่มสำหรับโหมด GCM
            byte[] iv = new byte[GCM_IV_LENGTH];
            new SecureRandom().nextBytes(iv);
            // สร้าง GCMParameterSpec ซึ่งเป็นพารามิเตอร์ที่จำเป็นสำหรับโหมด GCM (ระบุขนาด Tag และ IV)
            GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);

            // สร้าง Cipher สำหรับการเข้ารหัสด้วย AES
            Cipher aesCipher = Cipher.getInstance(AES_ALGORITHM);
            // เริ่มต้น Cipher ในโหมดเข้ารหัส โดยใช้ AES Key และ GCM parameters
            aesCipher.init(Cipher.ENCRYPT_MODE, aesKey, gcmParameterSpec);
            // ทำการเข้ารหัสข้อมูลจริง (plaintext)
            byte[] encryptedDataBytes = aesCipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
            // แปลงผลลัพธ์การเข้ารหัสข้อมูลเป็น Base64 String
            String encryptedDataBase64 = Base64.getEncoder().encodeToString(encryptedDataBytes);
            // แปลง IV เป็น Base64 String เพื่อส่งไปพร้อมกับข้อมูล
            String ivBase64 = Base64.getEncoder().encodeToString(iv);

            // --- ขั้นตอนที่ 4: รวบรวมข้อมูลที่เข้ารหัสทั้งหมดลงใน Object และแปลงเป็น JSON ---
            // สร้าง Object ที่จะเก็บผลลัพธ์ทั้งหมด (Encrypted AES Key, Encrypted Payload, IV)
            HybridEncryptedData result = new HybridEncryptedData(encryptedAesKeyBase64, encryptedDataBase64, ivBase64);
            // แปลง Object ผลลัพธ์เป็น JSON String เพื่อส่งกลับ
            return objectMapper.writeValueAsString(result);

        } catch (Exception e) {
            // หากเกิดข้อผิดพลาดใดๆ ในกระบวนการ ให้ log error และ throw exception ของระบบ
            logger.error("Error during hybrid encryption", e);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "Encryption failed");
        }
    }

    public String decrypt(String encryptedJson, String privateKeyStr) throws TMBCommonException {
        try {
            // --- ขั้นตอนที่ 1: แกะข้อมูลจาก JSON ที่ได้รับมา ---
            // แปลง JSON string กลับเป็น Object HybridEncryptedData
            HybridEncryptedData encryptedData = objectMapper.readValue(encryptedJson, HybridEncryptedData.class);
            // ถอดรหัส Base64 ของ Encrypted AES Key กลับเป็น byte array
            byte[] encryptedAesKeyBytes = Base64.getDecoder().decode(encryptedData.getEncryptedKey());
            // ถอดรหัส Base64 ของ Encrypted Payload กลับเป็น byte array
            byte[] encryptedPayloadBytes = Base64.getDecoder().decode(encryptedData.getEncryptedPayload());
            // ถอดรหัส Base64 ของ IV กลับเป็น byte array
            byte[] iv = Base64.getDecoder().decode(encryptedData.getIv());

            // --- ขั้นตอนที่ 2: ถอดรหัส AES Key ด้วย RSA Private Key ---
            // โหลด RSA Private Key จาก String ที่ได้รับมา
            PrivateKey rsaPrivateKey = loadPrivateKey(privateKeyStr);
            // สร้าง Cipher สำหรับการถอดรหัสด้วย RSA
            Cipher rsaCipher = Cipher.getInstance(RSA_ALGORITHM);
            // เริ่มต้น Cipher ในโหมดถอดรหัส (DECRYPT_MODE) โดยใช้ RSA Private Key
            rsaCipher.init(Cipher.DECRYPT_MODE, rsaPrivateKey);
            // ทำการถอดรหัส AES Key
            byte[] decryptedAesKeyBytes = rsaCipher.doFinal(encryptedAesKeyBytes);
            // สร้าง SecretKey object ของ AES จาก byte ที่ถอดรหัสได้
            SecretKey aesKey = new javax.crypto.spec.SecretKeySpec(decryptedAesKeyBytes, "AES");

            // --- ขั้นตอนที่ 3: ถอดรหัสข้อมูลจริง (Payload) ด้วย AES Key ---
            // สร้าง GCMParameterSpec จาก IV และ Tag length ที่ได้รับมา
            GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
            // สร้าง Cipher สำหรับการถอดรหัสด้วย AES
            Cipher aesCipher = Cipher.getInstance(AES_ALGORITHM);
            // เริ่มต้น Cipher ในโหมดถอดรหัส โดยใช้ AES Key และ GCM parameters
            aesCipher.init(Cipher.DECRYPT_MODE, aesKey, gcmParameterSpec);
            // ทำการถอดรหัสข้อมูลจริง
            byte[] decryptedDataBytes = aesCipher.doFinal(encryptedPayloadBytes);

            // แปลงผลลัพธ์ที่ถอดรหัสแล้วกลับเป็น String
            return new String(decryptedDataBytes, StandardCharsets.UTF_8);

        } catch (Exception e) {
            // หากเกิดข้อผิดพลาดใดๆ ในกระบวนการ ให้ log error และ throw exception ของระบบ
            logger.error("Error during hybrid decryption", e);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "Decryption failed");
        }
    }

    private PrivateKey loadPrivateKey(String key) throws NoSuchAlgorithmException, InvalidKeySpecException {
        byte[] keyBytes = Base64.getDecoder().decode(key);
        PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory kf = KeyFactory.getInstance("RSA");
        return kf.generatePrivate(spec);
    }

    private PublicKey loadPublicKey(String key) throws NoSuchAlgorithmException, InvalidKeySpecException {
        byte[] keyBytes = Base64.getDecoder().decode(key);
        X509EncodedKeySpec spec = new X509EncodedKeySpec(keyBytes);
        KeyFactory kf = KeyFactory.getInstance("RSA");
        return kf.generatePublic(spec);
    }
}
