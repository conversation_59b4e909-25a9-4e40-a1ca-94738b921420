package com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation;

import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPayment;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.BillPayExternalValidateResponse;

import java.math.BigDecimal;

import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_EASY_PASS;
import static com.tmb.oneapp.commonpaymentexp.utils.WowPointUtils.isWowPointTransaction;

public class ValidationCommonPaymentResponseMapper {
    private static final TMBLogger<ValidationCommonPaymentResponseMapper> logger = new TMBLogger<>(ValidationCommonPaymentResponseMapper.class);
    public static final ValidationCommonPaymentResponseMapper INSTANCE = new ValidationCommonPaymentResponseMapper();

    public ValidationCommonPaymentResponse mapToValidationCommonPaymentResponseForOnlineOfflineTopUp(ValidationCommonPaymentRequest request,
                                                                                                     CommonPaymentDraftCache cache,
                                                                                                     BillPayExternalValidateResponse externalResponse,
                                                                                                     BillPayValidateDataAfterCallExternal validateDataAfterCallExternal) {
        final OCPBillPayment ocpDataResponse = externalResponse.getOcpDataResponse();
        final String compCode = cache.getPaymentInformation().getCompCode();
        final boolean isRequireCommonAuthen = validateDataAfterCallExternal.isRequireCommonAuthen();
        final BigDecimal fee = validateDataAfterCallExternal.getFeeAfterCalculated();
        final var commonAuthentication = validateDataAfterCallExternal.getCommonAuthentication();
        final BigDecimal totalAmount = validateDataAfterCallExternal.getTotalAmount();

        ValidationCommonPaymentResponse response = new ValidationCommonPaymentResponse();
        response.setTransactionId(request.getTransactionId());
        response.setFee(fee);
        response.setAmount(new BigDecimal(ocpDataResponse.getAmount()));
        response.setIsRequireCommonAuthen(isRequireCommonAuthen);
        response.setTotalAmount(totalAmount);

        if (isRequireCommonAuthen) {
            response.setCommonAuthenticationInformation(commonAuthentication);
        }

        if (compCode.equals(BILL_COMP_CODE_EASY_PASS)) {
            response.setEasyPass(new EasyPassValidationCommonPaymentResponse()
                    .setEasyPassAccountName(validateDataAfterCallExternal.getEasyPassAccountName())
                    .setEasyPassTopUpRef(ocpDataResponse.getBankRefId()));
        }

        if (isWowPointTransaction(request, cache.getCommonPaymentRule())) {
            response.setWowPoint(WowPointValidationCommonPaymentResponseMapper.INSTANCE.mapWowPointValidationCommonPaymentResponse(request));
        }

        return response;
    }

    public ValidationCommonPaymentResponse mapToValidationCommonPaymentResponseForMEA(ValidationCommonPaymentRequest request, CommonPaymentDraftCache cache, BillPayExternalValidateResponse externalResponse, BillPayValidateDataAfterCallExternal validateDataAfterCallExternal) {
        final OCPBillPayment ocpDataResponse = externalResponse.getOcpDataResponse();
        final BigDecimal fee = validateDataAfterCallExternal.getFeeAfterCalculated();
        final boolean isRequireCommonAuthen = validateDataAfterCallExternal.isRequireCommonAuthen();
        final var commonAuthentication = validateDataAfterCallExternal.getCommonAuthentication();
        final BigDecimal totalAmount = validateDataAfterCallExternal.getTotalAmount();

        ValidationCommonPaymentResponse response = new ValidationCommonPaymentResponse();
        response.setFee(fee);
        response.setTransactionId(request.getTransactionId());
        response.setAmount(new BigDecimal(ocpDataResponse.getAmount()));
        response.setIsRequireCommonAuthen(isRequireCommonAuthen);
        response.setTotalAmount(totalAmount);

        if (isRequireCommonAuthen) {
            response.setCommonAuthenticationInformation(commonAuthentication);
        }

        if (isWowPointTransaction(request, cache.getCommonPaymentRule())) {
            response.setWowPoint(WowPointValidationCommonPaymentResponseMapper.INSTANCE.mapWowPointValidationCommonPaymentResponse(request));
        }

        return response;

    }

    public ValidationCommonPaymentResponse mapToValidationCommonPaymentResponseForMWA(ValidationCommonPaymentRequest request, CommonPaymentDraftCache cache, BillPayExternalValidateResponse externalResponse, BillPayValidateDataAfterCallExternal validateDataAfterCallExternal) {
        final OCPBillPayment ocpDataResponse = externalResponse.getOcpDataResponse();
        final BigDecimal amount = new BigDecimal(ocpDataResponse.getAmount());
        final BigDecimal fee = validateDataAfterCallExternal.getFeeAfterCalculated();
        final BigDecimal totalAmount = validateDataAfterCallExternal.getTotalAmount();

        ValidationCommonPaymentResponse response = new ValidationCommonPaymentResponse()
                .setTransactionId(request.getTransactionId())
                .setFee(fee)
                .setAmount(amount)
                .setTotalAmount(totalAmount)
                .setIsRequireCommonAuthen(validateDataAfterCallExternal.isRequireCommonAuthen())
                .setCommonAuthenticationInformation(validateDataAfterCallExternal.isRequireCommonAuthen() ?
                        validateDataAfterCallExternal.getCommonAuthentication() : null);

        if (isWowPointTransaction(request, cache.getCommonPaymentRule())) {
            response.setWowPoint(WowPointValidationCommonPaymentResponseMapper.INSTANCE.mapWowPointValidationCommonPaymentResponse(request));
        }

        return response;
    }

    public ValidationCommonPaymentResponse mapToValidationCommonPaymentResponseForPEA(ValidationCommonPaymentRequest request, CommonPaymentDraftCache cache, BillPayExternalValidateResponse externalResponse, BillPayValidateDataAfterCallExternal validateDataAfterCallExternal) {
        final OCPBillPayment ocpDataResponse = externalResponse.getOcpDataResponse();
        final BigDecimal amount = new BigDecimal(ocpDataResponse.getAmount());
        final BigDecimal fee = validateDataAfterCallExternal.getFeeAfterCalculated();
        final BigDecimal totalAmount = validateDataAfterCallExternal.getTotalAmount();

        ValidationCommonPaymentResponse response = new ValidationCommonPaymentResponse()
                .setTransactionId(request.getTransactionId())
                .setFee(fee)
                .setAmount(amount)
                .setTotalAmount(totalAmount)
                .setIsRequireCommonAuthen(validateDataAfterCallExternal.isRequireCommonAuthen())
                .setCommonAuthenticationInformation(validateDataAfterCallExternal.isRequireCommonAuthen() ?
                        validateDataAfterCallExternal.getCommonAuthentication() : null);

        if (isWowPointTransaction(request, cache.getCommonPaymentRule())) {
            response.setWowPoint(WowPointValidationCommonPaymentResponseMapper.INSTANCE.mapWowPointValidationCommonPaymentResponse(request));
        }

        return response;
    }
}
