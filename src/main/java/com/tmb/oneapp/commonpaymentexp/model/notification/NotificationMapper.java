package com.tmb.oneapp.commonpaymentexp.model.notification;

import com.tmb.oneapp.commonpaymentexp.model.cache.MasterBillerResponseInCache;
import com.tmb.oneapp.commonpaymentexp.model.cache.ReferenceResponseInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.utils.WowPointUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.Optional;

import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_CATEGORY_CODE_CREDIT_CARD;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.CATEGORY_E_DONATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.NotificationConstant.COMMAS_FORMAT;
import static com.tmb.oneapp.commonpaymentexp.constant.NotificationConstant.NOTIFICATION_ACCOUNT_METHOD_EN;
import static com.tmb.oneapp.commonpaymentexp.constant.NotificationConstant.NOTIFICATION_ACCOUNT_METHOD_TH;
import static com.tmb.oneapp.commonpaymentexp.constant.NotificationConstant.NOTIFICATION_CREDIT_CARD_METHOD_EN;
import static com.tmb.oneapp.commonpaymentexp.constant.NotificationConstant.NOTIFICATION_CREDIT_CARD_METHOD_TH;
import static com.tmb.oneapp.commonpaymentexp.constant.NotificationConstant.NOTIFICATION_TEMPLATE_COMMON_PAYMENT_COMPLETE;
import static com.tmb.oneapp.commonpaymentexp.constant.NotificationConstant.PATTERN_DATE_WITH_TIME_EN;
import static com.tmb.oneapp.commonpaymentexp.constant.NotificationConstant.PATTERN_DATE_WITH_TIME_TH;
import static com.tmb.oneapp.commonpaymentexp.constant.NotificationConstant.TH_LOWER_CASE;
import static com.tmb.oneapp.commonpaymentexp.constant.NotificationConstant.TH_UPPER_CASE;
import static com.tmb.oneapp.commonpaymentexp.utils.NullSafeUtils.getSafeNull;
import static com.tmb.oneapp.commonpaymentexp.utils.NullSafeUtils.getSafeNullOrDefault;


public class NotificationMapper {
    public static final NotificationMapper INSTANCE = new NotificationMapper();

    private NotificationMapper() {
    }

    public NotificationCommonPayment toCommonPaymentNotification(CommonPaymentDraftCache draftCache, String transactionRefNo, String crmId, String correlationId, String transactionTime) {
        NotificationCommonPayment result = new NotificationCommonPayment(NOTIFICATION_TEMPLATE_COMMON_PAYMENT_COMPLETE, crmId, correlationId);
        MasterBillerResponseInCache masterBillerResponse = getSafeNullOrDefault(() -> draftCache.getValidateDraftCache().getMasterBillerResponse(), new MasterBillerResponseInCache());
        var validateRequest = getSafeNullOrDefault(draftCache::getValidateRequest, new ValidationCommonPaymentRequest());
        boolean isPayWithCreditCard = getSafeNullOrDefault(() -> draftCache.getValidateRequest().getCreditCard().isPayWithCreditCardFlag(), false);
        String ref1Value = getSafeNull(() -> draftCache.getPaymentInformation().getProductDetail().getProductRef1());
        String compCode = getSafeNull(() -> draftCache.getPaymentInformation().getCompCode());
        String billerCategoryCode = getSafeNull(() -> masterBillerResponse.getBillerInfo().getBillerCategoryCode());

        if (StringUtils.equals(BILLER_CATEGORY_CODE_CREDIT_CARD, billerCategoryCode)) {
            ref1Value = this.masking(ref1Value);
        }

        result.setCompcode(compCode)
                .setReference1EN(this.composeRefLabelENAndValue(masterBillerResponse.getRef1(), ref1Value))
                .setReference1TH(this.composeRefLabelTHAndValue(masterBillerResponse.getRef1(), ref1Value))
                .setProductNameTH(getSafeNull(() -> draftCache.getPaymentInformation().getProductDetail().getProductNameTh()))
                .setProductNameEN(getSafeNull(() -> draftCache.getPaymentInformation().getProductDetail().getProductNameEn()))
                .setCouponDiscount("-")
                .setCouponCode("-")
                .setWowDiscount("-")
                .setWowPoint("-")
                .setCreditCardDiscount("-")
                .setCreditCardPoint("-")
                .setNetAmount(this.insertCommas(getSafeNull(() -> draftCache.getValidateDraftCache().getTotalAmount())))
                .setFee(this.insertCommas(getSafeNull(() -> draftCache.getValidateDraftCache().getFeeCalculated())));
        result.setTransactionRefNo(transactionRefNo);

        this.setDateTime(result, transactionTime);
        this.setNote(result, validateRequest.getNote());

        if (masterBillerResponse.getRef2() != null) {
            String ref2Value = getSafeNull(() -> draftCache.getPaymentInformation().getProductDetail().getProductRef2());

            if (StringUtils.equals(CATEGORY_E_DONATION_ID, billerCategoryCode)) {
                ref2Value = this.masking(ref2Value);
            }

            result.setReference2EN(this.composeRefLabelENAndValue(masterBillerResponse.getRef2(), ref2Value))
                    .setReference2TH(this.composeRefLabelTHAndValue(masterBillerResponse.getRef2(), ref2Value));
        }

        if (isPayWithCreditCard) {
            result.setMethodTH(NOTIFICATION_CREDIT_CARD_METHOD_TH)
                    .setMethodEN(NOTIFICATION_CREDIT_CARD_METHOD_EN)
                    .setCardId(this.getLastFourDigits(getSafeNull(() -> validateRequest.getCreditCard().getAccountId())))
                    .setAmount(this.insertCommas(getSafeNull(() -> draftCache.getValidateRequest().getCreditCard().getAmount())));
        } else {
            result.setMethodTH(NOTIFICATION_ACCOUNT_METHOD_TH)
                    .setMethodEN(NOTIFICATION_ACCOUNT_METHOD_EN)
                    .setFromAcctId(this.masking(getSafeNull(() -> validateRequest.getDeposit().getAccountNumber())));
            result.setAmount(this.insertCommas(getSafeNull(() -> draftCache.getValidateRequest().getDeposit().getAmount())));
        }

        if (WowPointUtils.isWowPointTransaction(validateRequest, draftCache.getCommonPaymentRule())) {
            result.setWowDiscount(this.insertCommas(draftCache.getValidateRequest().getWowPoint().getDiscountAmount()));
            result.setWowPoint(String.valueOf(draftCache.getValidateRequest().getWowPoint().getWowPointAmount()));
        }

        return result;
    }

    public NotificationCommonPayment toAutoLoanNotification(CommonPaymentDraftCache draftCache, String crmId, String correlationId, String transactionTime) {
        NotificationCommonPayment result = new NotificationCommonPayment(NOTIFICATION_TEMPLATE_COMMON_PAYMENT_COMPLETE, crmId, correlationId);
        MasterBillerResponseInCache masterBillerResponse = getSafeNullOrDefault(() -> draftCache.getValidateDraftCache().getMasterBillerResponse(), new MasterBillerResponseInCache());
        var validateRequest = getSafeNullOrDefault(draftCache::getValidateRequest, new ValidationCommonPaymentRequest());
        String transactionRefNo = getSafeNull(() -> draftCache.getValidateDraftCache().getExternalConfirmRequest().getTopUpETEPaymentRequest().getEpayCode());
        boolean isPayWithCreditCard = getSafeNullOrDefault(() -> draftCache.getValidateRequest().getCreditCard().isPayWithCreditCardFlag(), false);
        String ref1Value = getSafeNull(() -> draftCache.getPaymentInformation().getProductDetail().getProductRef1());
        String compCode = getSafeNull(() -> draftCache.getPaymentInformation().getCompCode());
        String billerCategoryCode = getSafeNull(() -> masterBillerResponse.getBillerInfo().getBillerCategoryCode());

        if (StringUtils.equals(BILLER_CATEGORY_CODE_CREDIT_CARD, billerCategoryCode)) {
            ref1Value = this.masking(ref1Value);
        }

        result.setCompcode(compCode)
                .setReference1EN(this.composeRefLabelENAndValue(masterBillerResponse.getRef1(), ref1Value))
                .setReference1TH(this.composeRefLabelTHAndValue(masterBillerResponse.getRef1(), ref1Value))
                .setProductNameTH(getSafeNull(() -> draftCache.getPaymentInformation().getProductDetail().getProductNameTh()))
                .setProductNameEN(getSafeNull(() -> draftCache.getPaymentInformation().getProductDetail().getProductNameEn()))
                .setCouponDiscount("-")
                .setCouponCode("-")
                .setWowDiscount("-")
                .setWowPoint("-")
                .setCreditCardDiscount("-")
                .setCreditCardPoint("-")
                .setNetAmount(this.insertCommas(getSafeNull(() -> draftCache.getValidateDraftCache().getTotalAmount())))
                .setFee(this.insertCommas(getSafeNull(() -> draftCache.getValidateDraftCache().getFeeCalculated())));
        result.setTransactionRefNo(transactionRefNo);

        this.setDateTime(result, transactionTime);
        this.setNote(result, validateRequest.getNote());

        if (masterBillerResponse.getRef2() != null) {
            String ref2Value = getSafeNull(() -> draftCache.getPaymentInformation().getProductDetail().getProductRef2());

            result.setReference2EN(this.composeRefLabelENAndValue(masterBillerResponse.getRef2(), ref2Value))
                    .setReference2TH(this.composeRefLabelTHAndValue(masterBillerResponse.getRef2(), ref2Value));
        }

        if (isPayWithCreditCard) {
            result.setMethodTH(NOTIFICATION_CREDIT_CARD_METHOD_TH)
                    .setMethodEN(NOTIFICATION_CREDIT_CARD_METHOD_EN)
                    .setCardId(this.getLastFourDigits(getSafeNull(() -> validateRequest.getCreditCard().getAccountId())))
                    .setAmount(this.insertCommas(getSafeNull(() -> draftCache.getValidateRequest().getCreditCard().getAmount())));
        } else {
            result.setMethodTH(NOTIFICATION_ACCOUNT_METHOD_TH)
                    .setMethodEN(NOTIFICATION_ACCOUNT_METHOD_EN)
                    .setFromAcctId(this.masking(getSafeNull(() -> validateRequest.getDeposit().getAccountNumber())));
            result.setAmount(this.insertCommas(getSafeNull(() -> draftCache.getValidateRequest().getDeposit().getAmount())));
        }

        return result;
    }

    public NotificationCommonPayment toCreditCardNotification(CommonPaymentDraftCache draftCache, String crmId, String correlationId, String transactionTime) {
        NotificationCommonPayment result = new NotificationCommonPayment(NOTIFICATION_TEMPLATE_COMMON_PAYMENT_COMPLETE, crmId, correlationId);
        MasterBillerResponseInCache masterBillerResponse = getSafeNullOrDefault(() -> draftCache.getValidateDraftCache().getMasterBillerResponse(), new MasterBillerResponseInCache());
        var validateRequest = getSafeNullOrDefault(draftCache::getValidateRequest, new ValidationCommonPaymentRequest());
        String transactionRefNo = getSafeNull(() -> draftCache.getValidateDraftCache().getExternalConfirmRequest().getCreditCardConfirmRequest().getBillPayment().getEpayCode());
        boolean isPayWithCreditCard = getSafeNullOrDefault(() -> draftCache.getValidateRequest().getCreditCard().isPayWithCreditCardFlag(), false);
        String ref1Value = getSafeNull(() -> draftCache.getPaymentInformation().getProductDetail().getProductRef1());
        String compCode = getSafeNull(() -> draftCache.getPaymentInformation().getCompCode());
        String billerCategoryCode = getSafeNull(() -> masterBillerResponse.getBillerInfo().getBillerCategoryCode());

        if (StringUtils.equals(BILLER_CATEGORY_CODE_CREDIT_CARD, billerCategoryCode)) {
            ref1Value = this.masking(ref1Value);
        }

        result.setCompcode(compCode)
                .setReference1EN(this.composeRefLabelENAndValue(masterBillerResponse.getRef1(), ref1Value))
                .setReference1TH(this.composeRefLabelTHAndValue(masterBillerResponse.getRef1(), ref1Value))
                .setProductNameTH(getSafeNull(() -> draftCache.getPaymentInformation().getProductDetail().getProductNameTh()))
                .setProductNameEN(getSafeNull(() -> draftCache.getPaymentInformation().getProductDetail().getProductNameEn()))
                .setCouponDiscount("-")
                .setCouponCode("-")
                .setWowDiscount("-")
                .setWowPoint("-")
                .setCreditCardDiscount("-")
                .setCreditCardPoint("-")
                .setNetAmount(this.insertCommas(getSafeNull(() -> draftCache.getValidateDraftCache().getTotalAmount())))
                .setFee(this.insertCommas(getSafeNull(() -> draftCache.getValidateDraftCache().getFeeCalculated())));
        result.setTransactionRefNo(transactionRefNo);

        this.setDateTime(result, transactionTime);
        this.setNote(result, validateRequest.getNote());

        if (masterBillerResponse.getRef2() != null) {
            String ref2Value = getSafeNull(() -> draftCache.getPaymentInformation().getProductDetail().getProductRef2());

            result.setReference2EN(this.composeRefLabelENAndValue(masterBillerResponse.getRef2(), ref2Value))
                    .setReference2TH(this.composeRefLabelTHAndValue(masterBillerResponse.getRef2(), ref2Value));
        }

        if (isPayWithCreditCard) {
            result.setMethodTH(NOTIFICATION_CREDIT_CARD_METHOD_TH)
                    .setMethodEN(NOTIFICATION_CREDIT_CARD_METHOD_EN)
                    .setCardId(this.getLastFourDigits(getSafeNull(() -> validateRequest.getCreditCard().getAccountId())))
                    .setAmount(this.insertCommas(getSafeNull(() -> draftCache.getValidateRequest().getCreditCard().getAmount())));
        } else {
            result.setMethodTH(NOTIFICATION_ACCOUNT_METHOD_TH)
                    .setMethodEN(NOTIFICATION_ACCOUNT_METHOD_EN)
                    .setFromAcctId(this.masking(getSafeNull(() -> validateRequest.getDeposit().getAccountNumber())));
            result.setAmount(this.insertCommas(getSafeNull(() -> draftCache.getValidateRequest().getDeposit().getAmount())));
        }

        return result;
    }


    private void setDateTime(NotificationPayment result, String transactionTime) {
        Date date = new Date(Long.parseLong(transactionTime));

        result.setAddDateTimeTH(new SimpleDateFormat(PATTERN_DATE_WITH_TIME_TH, new Locale(TH_LOWER_CASE, TH_UPPER_CASE)).format(date));
        result.setAddDateTimeEN(new SimpleDateFormat(PATTERN_DATE_WITH_TIME_EN).format(date));
    }

    private String composeRefLabelENAndValue(ReferenceResponseInCache referenceFromMasterBiller, String ref1Value) {
        String refLabelEN = Optional.ofNullable(referenceFromMasterBiller).map(ReferenceResponseInCache::getLabelEn).orElse(null);
        return StringUtils.joinWith(": ", refLabelEN, ref1Value);
    }

    private String composeRefLabelTHAndValue(ReferenceResponseInCache referenceFromMasterBiller, String ref1Value) {
        String refLabelTH = Optional.ofNullable(referenceFromMasterBiller).map(ReferenceResponseInCache::getLabelTh).orElse(null);
        return StringUtils.joinWith(": ", refLabelTH, ref1Value);
    }

    private String masking(String data) {
        if (data == null) {
            return null;
        }

        if (StringUtils.length(data) <= 3) {
            return data;
        }
        return "xx" + data.substring(data.length() - 4);
    }

    private String getLastFourDigits(String data) {
        if (data == null) {
            return null;
        }

        if (StringUtils.length(data) <= 3) {
            return data;
        }
        return data.substring(data.length() - 4);
    }

    private String insertCommas(BigDecimal number) {
        if (number == null) {
            return null;
        }
        COMMAS_FORMAT.setRoundingMode(RoundingMode.DOWN);
        return COMMAS_FORMAT.format(number);
    }

    private String insertCommas(String number) {
        if (number == null) {
            return null;
        }
        COMMAS_FORMAT.setRoundingMode(RoundingMode.DOWN);
        return COMMAS_FORMAT.format(new BigDecimal(number));
    }

    private void setNote(NotificationPayment result, String note) {
        result.setNote(StringUtils.isBlank(note) ? "-" : note);
    }
}
