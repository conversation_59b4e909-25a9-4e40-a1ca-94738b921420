package com.tmb.oneapp.commonpaymentexp.model.activitylog;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.model.BaseEvent;
import org.springframework.http.HttpHeaders;

import java.math.BigDecimal;
import java.math.RoundingMode;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.ACTIVITY_FAILURE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.ACTIVITY_SUCCESS;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_APP_VERSION;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CHANNEL;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_DEVICE_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_DEVICE_MODEL;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_IP_ADDRESS;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_OS_VERSION;
import static com.tmb.oneapp.commonpaymentexp.utils.NumberUtils.COMMAS_FORMAT;

public abstract class BaseActivityEvent extends BaseEvent {

    protected BaseActivityEvent(HttpHeaders headers) {
        super.setActivityDate(Long.toString(System.currentTimeMillis()));
        super.setIpAddress(headers.getFirst(HEADER_IP_ADDRESS));
        super.setOsVersion(headers.getFirst(HEADER_OS_VERSION));
        super.setChannel(headers.getFirst(HEADER_CHANNEL));
        super.setAppVersion(headers.getFirst(HEADER_APP_VERSION));
        super.setDeviceId(headers.getFirst(HEADER_DEVICE_ID));
        super.setDeviceModel(headers.getFirst(HEADER_DEVICE_MODEL));
        super.setCrmId(headers.getFirst(HEADER_CRM_ID));
        super.setCorrelationId(headers.getFirst(HEADER_CORRELATION_ID));
        super.setActivityStatus(ACTIVITY_SUCCESS);
    }


    public void setFailureStatusWithReasonFromException(Exception e) {
        super.setActivityStatus(ACTIVITY_FAILURE);
        if (e instanceof TMBCommonException ex) {
            setFailReason((ex.getErrorCode() + " : " + ex.getErrorMessage()));
        } else if (e.getCause() instanceof TMBCommonException ex) {
            setFailReason(String.format("%s : %s", ex.getErrorCode(), ex.getErrorMessage()));
        } else if (e instanceof TMBCommonExceptionWithResponse ex) {
            setFailReason(String.format("%s : %s", ex.getErrorCode(), ex.getErrorMessage()));
        } else if (e.getCause() instanceof TMBCommonExceptionWithResponse ex) {
            setFailReason(String.format("%s : %s", ex.getErrorCode(), ex.getErrorMessage()));
        } else {
            setFailReason(e.getClass().getSimpleName());
        }
    }

    public String insertCommas(BigDecimal number) {
        if (number == null) {
            return null;
        }
        COMMAS_FORMAT.setRoundingMode(RoundingMode.DOWN);

        return COMMAS_FORMAT.format(number);
    }
}
