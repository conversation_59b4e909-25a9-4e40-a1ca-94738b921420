package com.tmb.oneapp.commonpaymentexp.model.financiallog;

import com.tmb.common.constants.TmbCommonUtilityConstants;
import com.tmb.oneapp.commonpaymentexp.model.cache.CreditCardSupplementaryInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.utils.WowPointUtils;


public abstract class FinancialCustomBillPayActivityLog extends BaseFinancialActivityLog {

    protected FinancialCustomBillPayActivityLog(String crmId, String refId, CommonPaymentDraftCache commonPaymentDraftCache, String correlationId, String transactionDateTime) {
        super(crmId, refId, correlationId, transactionDateTime, commonPaymentDraftCache.getValidateDraftCache().getMasterBillerResponse().getBillerInfo());
        setUpFields(commonPaymentDraftCache);
    }

    private void setUpFields(CommonPaymentDraftCache commonPaymentDraftCache) {
        if (Boolean.TRUE.equals(commonPaymentDraftCache.getValidateRequest().getCreditCard().isPayWithCreditCardFlag())) {
            final CreditCardSupplementaryInCache creditCardDetail = commonPaymentDraftCache.getValidateDraftCache().getFromCreditCardDetail();
            setFinFlexValues1(commonPaymentDraftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest().getFromAccount().getAccountId());
            String creditCardNickname = creditCardDetail.getProductNameEn() != null ? creditCardDetail.getProductNameEn() : creditCardDetail.getProductNickname();
            setFromAccNickName(creditCardNickname);
            setFromAccName(creditCardDetail.getCardEmbossingName1());
            setFromAccNo(creditCardDetail.getCardNo());
            setFromAccType(TmbCommonUtilityConstants.ACCOUNT_TYPE_CCA);
        } else {
            setFromAccNickName(commonPaymentDraftCache.getValidateDraftCache().getFromDepositAccount().getProductNickname());
            setFromAccName(commonPaymentDraftCache.getValidateDraftCache().getFromDepositAccount().getAccountName());
            setFromAccNo(commonPaymentDraftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest().getFromAccount().getAccountId());
            setFromAccType(commonPaymentDraftCache.getValidateDraftCache().getFromDepositAccount().getAccountType());
            setFinFlexValues(commonPaymentDraftCache);
        }

        setMemo(commonPaymentDraftCache.getValidateRequest().getNote());
        setToAccNo(shortenAccountId(commonPaymentDraftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest().getToAccount().getAccountId()));

        setTxnAmount(commonPaymentDraftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest().getAmount());
        setTxnFee(commonPaymentDraftCache.getValidateDraftCache().getFeeCalculated().toString());
        setCompCode(commonPaymentDraftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest().getCompCode());
        setBillerRef1(commonPaymentDraftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest().getRef1());

        if (WowPointUtils.isWowPointTransaction(commonPaymentDraftCache.getValidateRequest(), commonPaymentDraftCache.getCommonPaymentRule())) {
            setTxnAmount(String.valueOf(commonPaymentDraftCache.getValidateDraftCache().getWowPointRedeemConfirmRequest().getTxnAmount()));
            setWowPointDiscount(String.valueOf(commonPaymentDraftCache.getValidateDraftCache().getWowPointRedeemConfirmRequest().getPointUnits()));
            setWowPointDiscountAmount(String.valueOf(commonPaymentDraftCache.getValidateDraftCache().getWowPointRedeemConfirmRequest().getAmount()));
            setTotalPayWithWowAmount(String.valueOf(commonPaymentDraftCache.getValidateDraftCache().getWowPointRedeemConfirmRequest().getTxnAmount()
                    .add(commonPaymentDraftCache.getValidateDraftCache().getWowPointRedeemConfirmRequest().getAmount())));
        }
    }

    protected abstract void setFinFlexValues(CommonPaymentDraftCache commonPaymentDraftCache);
}
