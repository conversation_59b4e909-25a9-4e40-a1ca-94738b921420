package com.tmb.oneapp.commonpaymentexp.model.financiallog;

import com.tmb.common.constants.TmbCommonUtilityConstants;
import com.tmb.oneapp.commonpaymentexp.model.cache.CreditCardSupplementaryInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.AdditionalParam;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.utils.WowPointUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;

import static com.tmb.common.constants.TmbCommonUtilityConstants.ACCOUNT_TYPE_LOC;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_AIS_ON_TOP_POSTPAID;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_AIS_ON_TOP_PREPAID;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_EASY_PASS;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.TOP_UP_COMP_CODE_TRUE_MOVE_H_DATA_PACKAGE;
import static com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils.getStringFromObject;


public class FinancialOCPBillPayActivityLog extends BaseFinancialActivityLog {
    private static final String D00 = "";
    private static final String BILLER_METHOD_TMB_LOAN = "3";
    private static final String PAYMENT_METHOD_TMB_PRODUCT = "15";

    private static final String PACKAGE_NAME = "packageName";
    private static final String PACKAGE_DETAIL = "packageDetail";
    private static final String PACKAGE_NAME_TH = "packageNameTh";
    private static final String PACKAGE_DETAIL_TH = "packageDetailTh";
    private static final String TELCO_PACKAGE = "TelcoPackage";

    public FinancialOCPBillPayActivityLog(String crmId, String refId, CommonPaymentDraftCache commonPaymentDraftCache, String correlationId, String transactionDateTime) {
        super(crmId, refId, correlationId, transactionDateTime, commonPaymentDraftCache.getValidateDraftCache().getMasterBillerResponse().getBillerInfo());
        setUpFields(commonPaymentDraftCache);
    }

    private void setUpFields(CommonPaymentDraftCache commonPaymentDraftCache) {
        setMemo(commonPaymentDraftCache.getValidateRequest().getNote());
        setAccountFields(commonPaymentDraftCache);
        setTransactionFields(commonPaymentDraftCache);
        setEasyPassFields(commonPaymentDraftCache);
        setTelcoPackageFields(commonPaymentDraftCache);
        setWowPointFields(commonPaymentDraftCache);
    }

    private void setAccountFields(CommonPaymentDraftCache commonPaymentDraftCache) {
        if (Boolean.TRUE.equals(commonPaymentDraftCache.getValidateRequest().getCreditCard().isPayWithCreditCardFlag())) {
            setCreditCardAccountFields(commonPaymentDraftCache);
        } else {
            setDepositAccountFields(commonPaymentDraftCache);
        }

        OCPBillRequest confirmRequest = commonPaymentDraftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest();
        setToAccNo(shortenAccountId(confirmRequest.getToAccount().getAccountId()));
        setToAccType(getAccountType(
                commonPaymentDraftCache.getValidateDraftCache().getMasterBillerResponse().getBillerInfo().getPaymentMethod(),
                commonPaymentDraftCache.getValidateDraftCache().getMasterBillerResponse().getBillerInfo().getBillerMethod()
        ));
    }

    private void setCreditCardAccountFields(CommonPaymentDraftCache commonPaymentDraftCache) {
        final CreditCardSupplementaryInCache creditCardDetail = commonPaymentDraftCache.getValidateDraftCache().getFromCreditCardDetail();
        setFinFlexValues1(commonPaymentDraftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest().getFromAccount().getAccountId());
        String creditCardNickname = creditCardDetail.getProductNameEn() != null ? creditCardDetail.getProductNameEn() : creditCardDetail.getProductNickname();
        setFromAccNickName(creditCardNickname);
        setFromAccName(creditCardDetail.getCardEmbossingName1());
        setFromAccNo(creditCardDetail.getCardNo());
        setFromAccType(TmbCommonUtilityConstants.ACCOUNT_TYPE_CCA);
    }

    private void setDepositAccountFields(CommonPaymentDraftCache commonPaymentDraftCache) {
        setFromAccNickName(commonPaymentDraftCache.getValidateDraftCache().getFromDepositAccount().getProductNickname());
        setFromAccName(commonPaymentDraftCache.getValidateDraftCache().getFromDepositAccount().getAccountName());
        setFromAccNo(commonPaymentDraftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest().getFromAccount().getAccountId());
        setFromAccType(commonPaymentDraftCache.getValidateDraftCache().getFromDepositAccount().getAccountType());
    }

    private void setTransactionFields(CommonPaymentDraftCache commonPaymentDraftCache) {
        OCPBillRequest confirmRequest = commonPaymentDraftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest();
        ProductDetail productDetail = commonPaymentDraftCache.getPaymentInformation().getProductDetail();
        setTxnAmount(confirmRequest.getAmount());
        setTxnFee(String.valueOf(commonPaymentDraftCache.getValidateDraftCache().getFeeCalculated()));
        setCompCode(confirmRequest.getCompCode());
        setBillerRef1(productDetail.getProductRef1());
        setBillerRef2(productDetail.getProductRef2());
    }

    private void setEasyPassFields(CommonPaymentDraftCache commonPaymentDraftCache) {
        OCPBillRequest confirmRequest = commonPaymentDraftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest();
        if (confirmRequest.getCompCode().equals(BILL_COMP_CODE_EASY_PASS)) {
            setBillerBalance(confirmRequest.getRef4());
            setFinLinkageId(confirmRequest.getInvoiceNum());
        }
    }

    private void setTelcoPackageFields(CommonPaymentDraftCache commonPaymentDraftCache) {
        OCPBillRequest confirmRequest = commonPaymentDraftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest();
        List<AdditionalParam> additionalParams = confirmRequest.getAdditionalParams();

        boolean isAisOnTop = StringUtils.equals(BILL_COMP_CODE_AIS_ON_TOP_PREPAID, confirmRequest.getCompCode())
                || StringUtils.equals(BILL_COMP_CODE_AIS_ON_TOP_POSTPAID, confirmRequest.getCompCode());
        boolean isTopUpTrueMoveHDataPackage = StringUtils.equals(TOP_UP_COMP_CODE_TRUE_MOVE_H_DATA_PACKAGE, confirmRequest.getCompCode());

        if ((isAisOnTop || isTopUpTrueMoveHDataPackage) && !CollectionUtils.isEmpty(additionalParams)) {
            setTelcoFinFlexValues(additionalParams);
        }
    }

    private void setTelcoFinFlexValues(List<AdditionalParam> additionalParams) {
        setFinFlexValues1(TELCO_PACKAGE);
        additionalParams.forEach(this::setTelcoPackageParam);
    }

    private void setTelcoPackageParam(AdditionalParam param) {
        switch (param.getName()) {
            case PACKAGE_NAME -> setFinFlexValues2(getStringFromObject(param.getValue()));
            case PACKAGE_DETAIL -> setFinFlexValues3(getStringFromObject(param.getValue()));
            case PACKAGE_NAME_TH -> setFinFlexValues4(getStringFromObject(param.getValue()));
            case PACKAGE_DETAIL_TH -> setFinFlexValues5(getStringFromObject(param.getValue()));
            default -> {
            }
        }
    }

    private String getAccountType(String paymentMethod, String billerMethod) {
        if (paymentMethod.equals(PAYMENT_METHOD_TMB_PRODUCT) && billerMethod.equals(BILLER_METHOD_TMB_LOAN)) {
            return ACCOUNT_TYPE_LOC;
        }

        return D00;
    }

    private void setWowPointFields(CommonPaymentDraftCache commonPaymentDraftCache) {
        if (WowPointUtils.isWowPointTransaction(commonPaymentDraftCache.getValidateRequest(), commonPaymentDraftCache.getCommonPaymentRule())) {
            setTxnAmount(String.valueOf(commonPaymentDraftCache.getValidateDraftCache().getWowPointRedeemConfirmRequest().getTxnAmount()));
            setWowPointDiscount(String.valueOf(commonPaymentDraftCache.getValidateDraftCache().getWowPointRedeemConfirmRequest().getPointUnits()));
            setWowPointDiscountAmount(String.valueOf(commonPaymentDraftCache.getValidateDraftCache().getWowPointRedeemConfirmRequest().getAmount()));
            setTotalPayWithWowAmount(String.valueOf(commonPaymentDraftCache.getValidateDraftCache().getWowPointRedeemConfirmRequest().getTxnAmount()
                    .add(commonPaymentDraftCache.getValidateDraftCache().getWowPointRedeemConfirmRequest().getAmount())));
        }
    }
}
