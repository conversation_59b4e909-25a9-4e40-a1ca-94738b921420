package com.tmb.oneapp.commonpaymentexp.controller.security;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitializationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitializationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.security.EncryptedPayloadRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.security.PublicKeyResponse;
import com.tmb.oneapp.commonpaymentexp.service.InitializationCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.PartnerIntegrationService;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@Tag(name = "Partner Integration API", description = "APIs for external partner integration")
public class PartnerIntegrationController {

    private final PartnerIntegrationService partnerIntegrationService;
    private final InitializationCommonPaymentService initializationCommonPaymentService;

    @Operation(summary = "Get public key set for a partner")
    @GetMapping("/public-key")
    public ResponseEntity<TmbServiceResponse<PublicKeyResponse>> getPublicKey(
            @RequestHeader(CommonPaymentExpConstant.HEADER_X_PARTNER_NAME) String partnerName) throws TMBCommonException {
        TmbServiceResponse<PublicKeyResponse> response = new TmbServiceResponse<>();
        PublicKeyResponse publicKeyResponse = partnerIntegrationService.getPublicKey(partnerName);
        response.setStatus(CommonServiceUtils.getStatusSuccess());
        response.setData(publicKeyResponse);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Initialize a payment with an encrypted payload")
    @PostMapping("/initial")
    public ResponseEntity<TmbServiceResponse<InitializationCommonPaymentResponse>> initialEncryptedPayment(
            @RequestHeader(CommonPaymentExpConstant.HEADER_X_PARTNER_NAME) String partnerName,
            @RequestBody @Valid EncryptedPayloadRequest encryptedRequest,
            @RequestHeader HttpHeaders headers) throws TMBCommonException {

        InitializationCommonPaymentRequest request = partnerIntegrationService.decryptInitialPayload(encryptedRequest.getPayload(), partnerName);
        InitializationCommonPaymentResponse data = initializationCommonPaymentService.initialCommonPayment(request, headers);

        TmbServiceResponse<InitializationCommonPaymentResponse> response = new TmbServiceResponse<>();
        response.setStatus(CommonServiceUtils.getStatusSuccess());
        response.setData(data);
        return ResponseEntity.ok(response);
    }
}
