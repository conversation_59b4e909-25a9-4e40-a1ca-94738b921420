package com.tmb.oneapp.commonpaymentexp.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.commonpaymentexp.logger.LogExecutionTime;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitializationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitializationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.paymetmethod.PaymentMethodCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.service.InitializationCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.ConfirmationCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.ValidationCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;

@RestController
@RequestMapping("/common-payment")
@Tag(name = "Common payment", description = "Common payment controller")
@RequiredArgsConstructor
public class CommonPaymentController {
    private static final TMBLogger<CommonPaymentController> logger = new TMBLogger<>(CommonPaymentController.class);
    private final InitializationCommonPaymentService initializationCommonPaymentService;
    private final PaymentMethodCommonPaymentService paymentMethodCommonPaymentService;
    private final ValidationCommonPaymentService validationCommonPaymentService;
    private final ConfirmationCommonPaymentService confirmationCommonPaymentService;

    @LogAround
    @LogExecutionTime
    @Operation(summary = "[STEP 1] : Initial common-payment")
    @PostMapping(value = "/initial", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<InitializationCommonPaymentResponse>> initialCommonPayment(
            @Parameter(name = HEADER_CORRELATION_ID, example = "c28f91e4-881e-4387-a597-4a39c2822b3c", required = true) @Valid @RequestHeader(HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(name = HEADER_CRM_ID, example = "001100000000000000000001184383") @Valid @RequestHeader(value = HEADER_CRM_ID, required = false) String crmId,
            @Valid @RequestBody InitializationCommonPaymentRequest initializationCommonPaymentRequest,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException {

        InitializationCommonPaymentResponse data = initializationCommonPaymentService.initialCommonPayment(initializationCommonPaymentRequest, headers);
        TmbServiceResponse<InitializationCommonPaymentResponse> response = new TmbServiceResponse<>();
        response.setStatus(CommonServiceUtils.getStatusSuccess());
        response.setData(data);

        return ResponseEntity.ok(response);
    }

    @LogAround
    @LogExecutionTime
    @Operation(summary = "[STEP 2] : Get common-payment method")
    @GetMapping(value = "/payment-method/{transaction-id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<PaymentMethodCommonPaymentResponse>> getCommonPaymentMethod(
            @Parameter(name = HEADER_CORRELATION_ID, example = "c28f91e4-881e-4387-a597-4a39c2822b3c", required = true) @Valid @RequestHeader(HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(name = HEADER_CRM_ID, example = "001100000000000000000001184383", required = true) @RequestHeader(HEADER_CRM_ID) @Valid @RequestHeaderNonNull String crmId,
            @PathVariable("transaction-id") String transactionId,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException {

        PaymentMethodCommonPaymentResponse data = paymentMethodCommonPaymentService.getCommonPaymentMethod(transactionId, headers);
        TmbServiceResponse<PaymentMethodCommonPaymentResponse> response = new TmbServiceResponse<>();
        response.setStatus(CommonServiceUtils.getStatusSuccess());
        response.setData(data);

        return ResponseEntity.ok(response);
    }

    @LogAround
    @LogExecutionTime
    @Operation(summary = "[STEP 3] : Validate common-payment")
    @PostMapping(value = "/validate", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<ValidationCommonPaymentResponse>> validateCommonPayment(
            @Parameter(name = HEADER_CORRELATION_ID, example = "c28f91e4-881e-4387-a597-4a39c2822b3c", required = true) @Valid @RequestHeader(HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(name = HEADER_CRM_ID, example = "001100000000000000000001184383", required = true) @RequestHeader(HEADER_CRM_ID) @Valid @RequestHeaderNonNull String crmId,
            @Valid @RequestBody ValidationCommonPaymentRequest validationCommonPaymentRequest,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException, TMBCommonExceptionWithResponse {

        ValidationCommonPaymentResponse data = validationCommonPaymentService.validateCommonPayment(validationCommonPaymentRequest, headers);
        TmbServiceResponse<ValidationCommonPaymentResponse> response = new TmbServiceResponse<>();
        response.setStatus(CommonServiceUtils.getStatusSuccess());
        response.setData(data);

        return ResponseEntity.ok(response);
    }

    @LogAround
    @LogExecutionTime
    @Operation(summary = "[STEP 4] : Confirm common-payment")
    @PostMapping(value = "/confirm", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<ConfirmationCommonPaymentResponse>> confirmCommonPayment(
            @Parameter(name = HEADER_CORRELATION_ID, example = "c28f91e4-881e-4387-a597-4a39c2822b3c", required = true) @Valid @RequestHeader(HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(name = HEADER_CRM_ID, example = "001100000000000000000001184383", required = true) @RequestHeader(HEADER_CRM_ID) @Valid @RequestHeaderNonNull String crmId,
            @RequestBody @Valid ConfirmationCommonPaymentRequest confirmationCommonPaymentRequest,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException, TMBCommonExceptionWithResponse {

        ConfirmationCommonPaymentResponse data = confirmationCommonPaymentService.confirmCommonPayment(confirmationCommonPaymentRequest, headers);
        TmbServiceResponse<ConfirmationCommonPaymentResponse> response = new TmbServiceResponse<>();
        response.setStatus(CommonServiceUtils.getStatusSuccess());
        response.setData(data);

        return ResponseEntity.ok(response);
    }
}
