package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentConfig;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.PaymentStatus;
import com.tmb.oneapp.commonpaymentexp.utils.JoseUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

@Service
@RequiredArgsConstructor
public class CallbackConfirmService {
    private static final TMBLogger<CallbackConfirmService> logger = new TMBLogger<>(CallbackConfirmService.class);
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final String SECRET_HEADER = "secret";

    public void callback(CommonPaymentConfig commonPaymentConfig, PaymentStatus paymentStatus) {
        if (ObjectUtils.anyNull(commonPaymentConfig, paymentStatus)) {
            logger.error("Invalid input parameters - config: {}, status: {}", commonPaymentConfig, paymentStatus);
            return;
        }
        try {
            HttpHeaders headers = createHeaders(commonPaymentConfig.getCallbackApiKey(), paymentStatus);
            HttpEntity<PaymentStatus> entity = new HttpEntity<>(paymentStatus, headers);

            ResponseEntity<TmbServiceResponse> response = restTemplate.exchange(commonPaymentConfig.getCallbackUrl(), HttpMethod.POST, entity, TmbServiceResponse.class);

            boolean isSuccess = response.getStatusCode().is2xxSuccessful();
            if (!isSuccess) {
                logger.error("Callback failed with status code: {}", response.getStatusCode());
            }

        } catch (RestClientException e) {
            logger.error("Failed to send callback - URL: {}, Error: {}", commonPaymentConfig.getCallbackUrl(), e.getMessage(), e);
        } catch (Exception e) {
            logger.error("Unexpected error during callback", e);
        }
    }

    private HttpHeaders createHeaders(String secretKey, PaymentStatus paymentStatus) throws TMBCommonException {
        HttpHeaders headers = new HttpHeaders();
        Map<String, Object> claims = objectMapper.convertValue(paymentStatus, new TypeReference<>() {
        });
        headers.set(SECRET_HEADER, JoseUtils.generateJws(secretKey, claims));
        headers.setContentType(MediaType.APPLICATION_JSON);
        return headers;
    }
}
