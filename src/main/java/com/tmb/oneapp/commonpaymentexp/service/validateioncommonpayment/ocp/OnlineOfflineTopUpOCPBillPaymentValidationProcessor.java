package com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.ocp;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.validation.ActivityBillPayMapper;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.validation.ActivityBillPayValidationEvent;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillPayConfiguration;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.cache.CacheMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenResult;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ExternalConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.AdditionalParam;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPayment;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequestMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.BillPayValidateDataAfterCallExternal;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CommonAuthenticationValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CreditCardValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentResponseMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointRedeemConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointRedeemConfirmRequestMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.BillPayExternalValidateResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.BillPayPrepareDataValidate;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.ValidationCommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardSupplementary;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.dailylimit.DailyLimitService;
import com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.ValidationProcessingTemplate;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.WowPointUtils;
import com.tmb.oneapp.commonpaymentexp.validator.BaseBillPayValidator;
import com.tmb.oneapp.commonpaymentexp.validator.DailyLimitPinFreeValidator;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.tmb.common.constants.TmbCommonUtilityConstants.ACCOUNT_TYPE_CCA;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_GROUP_TYPE_TOP_UP;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_EASY_PASS;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.COMP_CODE_REQUIRED_REF_VALIDATION_LIST;
import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_CACHE_PREFIX;
import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_HASH_KEY_CACHE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_AUTH_BILL_FLOW_NAME;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_AUTH_TOP_UP_FLOW_NAME;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_PAYMENT_BILL_PAY_FEATURE_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_PAYMENT_TOP_UP_FEATURE_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.BILL_ADDITIONAL_PARAM_KEY_NAME;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.BILL_PAYMENT_TOP_UP_CACHE_KEY;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_ONLINE_OFFLINE_TOPUP;
import static com.tmb.oneapp.commonpaymentexp.model.financiallog.BaseFinancialActivityLog.BILLER_GROUP_TOP_UP;
import static com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils.getUnhandledTmbCommonException;

@Service
@RequiredArgsConstructor
public class OnlineOfflineTopUpOCPBillPaymentValidationProcessor extends ValidationProcessingTemplate<
        BillPayPrepareDataValidate,
        BillPayExternalValidateResponse,
        BillPayValidateDataAfterCallExternal,
        ActivityBillPayValidationEvent> {
    private static final TMBLogger<OnlineOfflineTopUpOCPBillPaymentValidationProcessor> logger = new TMBLogger<>(OnlineOfflineTopUpOCPBillPaymentValidationProcessor.class);

    private final PaymentService paymentService;
    private final CacheService cacheService;
    private final LogEventPublisherService logEventPublisherService;
    private final DailyLimitPinFreeValidator dailyLimitPinFreeValidator;
    private final BaseBillPayValidator baseBillPayValidator;
    private final OCPValidationHelper ocpValidationHelper;
    private final DailyLimitService dailyLimitService;

    @Override
    public String getProcessorType() {
        return BILLER_PAYMENT_ONLINE_OFFLINE_TOPUP;
    }

    @Override
    protected BillPayExternalValidateResponse validateWithExternalService(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, BillPayPrepareDataValidate prepareData) throws TMBCommonException, TMBCommonExceptionWithResponse {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final DepositAccount fromDepositAccount = prepareData.getFromDepositAccount();
        final CreditCardSupplementary creditCardDetail = prepareData.getFromCreditCardDetail();
        final MasterBillerResponse masterBillerResponse = prepareData.getMasterBillerResponse();
        BillPayExternalValidateResponse externalValidateResponse = new BillPayExternalValidateResponse();

        OCPBillRequest ocpRequest = OCPBillRequestMapper.INSTANCE.toOCPBillRequestForValidateOCPBillPayment(request, fromDepositAccount, masterBillerResponse, cache, creditCardDetail);
        this.validateDuplicateRef(crmId, ocpRequest);

        OCPBillPayment ocpDataResponse = paymentService.validateOCPBillPayment(correlationId, crmId, ocpRequest).getData();

        externalValidateResponse.setOcpRequest(ocpRequest);
        externalValidateResponse.setOcpDataResponse(ocpDataResponse);
        return externalValidateResponse;
    }

    @Override
    protected void saveSuccessLog(ValidationCommonPaymentRequest request,
                                  HttpHeaders headers,
                                  CommonPaymentDraftCache cache,
                                  BillPayPrepareDataValidate prepareData,
                                  BillPayExternalValidateResponse externalResponse,
                                  BillPayValidateDataAfterCallExternal validateDataAfterCallExternal,
                                  ActivityBillPayValidationEvent activityEvent) {
        final MasterBillerResponse masterBillerResponse = prepareData.getMasterBillerResponse();
        final CreditCardSupplementary creditCardDetail = prepareData.getFromCreditCardDetail();

        ActivityBillPayValidationEvent activityEventData = activityEvent;
        activityEventData = ActivityBillPayMapper.INSTANCE.updateActivityBillPayValidation(activityEventData, masterBillerResponse, cache, request, creditCardDetail, validateDataAfterCallExternal);

        logEventPublisherService.saveActivityLog(activityEventData);
    }


    private String getFeatureId(MasterBillerResponse masterBillerResponse) {
        if (masterBillerResponse.getBillerInfo().getBillerGroupType().equals(BILLER_GROUP_TYPE_TOP_UP)) {
            return COMMON_PAYMENT_TOP_UP_FEATURE_ID;
        }
        return COMMON_PAYMENT_BILL_PAY_FEATURE_ID;
    }

    @Override
    protected BillPayPrepareDataValidate validateData(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, BillPayPrepareDataValidate prepareData) throws TMBCommonException {
        final BillPayConfiguration billPayConfiguration = prepareData.getBillPayConfiguration();
        final MasterBillerResponse masterBillerResponse = prepareData.getMasterBillerResponse();
        final CustomerCrmProfile customerCrmProfile = prepareData.getCustomerCrmProfile();
        final String compCode = cache.getPaymentInformation().getCompCode();
        final boolean isPayWithWowPoint = !ObjectUtils.isEmpty(cache.getCommonPaymentRule())
                && cache.getCommonPaymentRule().isWowPointFlag() && !ObjectUtils.isEmpty(request.getWowPoint());
        final boolean isNotPayWithCreditCard = !request.getCreditCard().isPayWithCreditCardFlag();

        this.checkCompCodeInExcludeBillerConfig(billPayConfiguration, compCode);
        this.transformRequestBody(cache.getPaymentInformation().getProductDetail());
        this.checkBillerExpired(masterBillerResponse);
        this.checkServiceHours(masterBillerResponse);
        this.validateSpecialBillerOffline(request, cache, masterBillerResponse);

        if (isNotPayWithCreditCard) {
            this.validateDailyLimitExceeded(request, masterBillerResponse, customerCrmProfile);
        }

        if (isPayWithWowPoint && isNotPayWithCreditCard) {
            baseBillPayValidator.validateWowPoint(request, cache);
        }

        return prepareData;
    }

    @Override
    protected ValidationCommonPaymentResponse mappingResponse(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, BillPayPrepareDataValidate prepareData, BillPayExternalValidateResponse externalResponse, BillPayValidateDataAfterCallExternal validateDataAfterCallExternal) {
        return ValidationCommonPaymentResponseMapper.INSTANCE.mapToValidationCommonPaymentResponseForOnlineOfflineTopUp(request, cache, externalResponse, validateDataAfterCallExternal);
    }

    /**
     * When comp code is 3069 or 3070 or 3071, check if "ref1" and "ref2" are already exist is Redis
     * Throw exception with error code "topup_duplicate_ref" when found refs in cache that matched with refs in request
     *
     * @param crmId      CRM ID from header
     * @param ocpRequest Request data
     * @throws TMBCommonException When references are duplicate, throws exception
     */
    protected void validateDuplicateRef(String crmId, OCPBillRequest ocpRequest) throws TMBCommonException {
        String compCode = ocpRequest.getCompCode();
        String ref1 = ocpRequest.getRef1();
        String ref2 = ocpRequest.getRef2();
        try {
            boolean shouldValidateRef = COMP_CODE_REQUIRED_REF_VALIDATION_LIST.contains(compCode);
            if (shouldValidateRef) {
                String topUpCacheKey = String.format(BILL_PAYMENT_TOP_UP_CACHE_KEY, compCode, crmId);
                Optional<String> dataOpt = Optional.ofNullable(cacheService.get(topUpCacheKey));
                if (dataOpt.isPresent()) {
                    Map<String, String> refMap =
                            TMBUtils.convertStringToJavaObjWithTypeReference(dataOpt.get(), new TypeReference<>() {
                            });
                    validateRef(refMap, ref1, ref2);
                }
            }
        } catch (TMBCommonException ex) {
            logger.error("Invalid payment, references are duplicate. ref1 [{}] ::: ref2 [{}]", ref1, ref2);
            throw ex;
        } catch (Exception ex) {
            logger.error("Failed to validate ref in validateDuplicateRef, with error {}", ex.getMessage(), ex);
        }
    }

    private void validateRef(Map<String, String> refMap, String ref1, String ref2) throws TMBCommonException {
        boolean isRef1Duplicate = StringUtils.equalsIgnoreCase(refMap.get("ref1"), ref1);
        boolean isRef2Duplicate = StringUtils.equalsIgnoreCase(refMap.get("ref2"), ref2);
        if (isRef1Duplicate && isRef2Duplicate) {
            throw CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.BILL_PAY_TOP_UP_DUPLICATE_REF);
        }
    }

    @Override
    protected ActivityBillPayValidationEvent initialActivityLog(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache) {
        return ActivityBillPayMapper.INSTANCE.toActivityBillPayValidation(headers, request, cache);
    }

    @Override
    protected BillPayPrepareDataValidate prepareData(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache) throws TMBCommonException {
        return ocpValidationHelper.basePrepareData(request, headers, cache);
    }

    private void checkCompCodeInExcludeBillerConfig(BillPayConfiguration configData, String compCode) throws TMBCommonException {
        baseBillPayValidator.validateCompCodeExclusion(configData, compCode);
    }

    private void transformRequestBody(ProductDetail productDetail) {
        productDetail.setProductRef1(StringUtils.upperCase(productDetail.getProductRef1()));
        productDetail.setProductRef2(StringUtils.upperCase(productDetail.getProductRef2()));
    }

    private void validateSpecialBillerOffline(ValidationCommonPaymentRequest request, CommonPaymentDraftCache cache, MasterBillerResponse masterBillerResponse) throws TMBCommonException {
        final String compCode = cache.getPaymentInformation().getCompCode();
        boolean isPayWithCreditCard = request.getCreditCard().isPayWithCreditCardFlag();
        BigDecimal amount = isPayWithCreditCard ? request.getCreditCard().getAmount() : request.getDeposit().getAmount();

        baseBillPayValidator.validateSpecialBillerOffline(compCode, cache.getPaymentInformation().getProductDetail().getProductRef1(), cache.getPaymentInformation().getProductDetail().getProductRef2(), amount, masterBillerResponse);
    }

    private void checkServiceHours(MasterBillerResponse masterBillerResponse) throws TMBCommonException {
        baseBillPayValidator.validateServiceHours(masterBillerResponse);
    }

    private void validateDailyLimitExceeded(ValidationCommonPaymentRequest request, MasterBillerResponse masterBiller, CustomerCrmProfile customerCrmProfile) throws TMBCommonException {
        BigDecimal amount = request.getDeposit().getAmount();
        dailyLimitService.validateDailyLimitExceeded(masterBiller.getBillerInfo().getBillerGroupType(), customerCrmProfile, amount);
    }

    private void checkBillerExpired(MasterBillerResponse masterBillerResponse) throws TMBCommonException {
        baseBillPayValidator.validateBillerExpiration(masterBillerResponse);
    }

    @Override
    protected BillPayValidateDataAfterCallExternal validateAfterCallExternalService(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, BillPayPrepareDataValidate prepareData, BillPayExternalValidateResponse externalResponse) throws TMBCommonException {
        final String compCode = cache.getPaymentInformation().getCompCode();
        final CustomerCrmProfile customerCrmProfile = prepareData.getCustomerCrmProfile();
        final OCPBillPayment ocpDataResponse = externalResponse.getOcpDataResponse();
        final CreditCardSupplementary creditCardDetail = prepareData.getFromCreditCardDetail();
        final DepositAccount fromDepositAccount = prepareData.getFromDepositAccount();
        final MasterBillerResponse masterBillerResponse = prepareData.getMasterBillerResponse();
        final boolean isPayWithCreditCard = request.getCreditCard().isPayWithCreditCardFlag();

        final BigDecimal fee = this.calculateFee(ocpDataResponse);

        baseBillPayValidator.validateInsufficientFund(request, creditCardDetail, fromDepositAccount, fee);

        CommonAuthenResult commonAuthenResult = this.validateIsRequireCommonAuthen(request, headers, isPayWithCreditCard, customerCrmProfile, masterBillerResponse);

        String easyPassAccountName = null;
        if (compCode.equals(BILL_COMP_CODE_EASY_PASS)) {
            easyPassAccountName = this.getEasyPassAccountNameFromTransaction(ocpDataResponse.getAdditionalParams());
        }

        CommonAuthenticationValidationCommonPaymentResponse commonAuthenResponse = null;
        if (commonAuthenResult.isRequireCommonAuthen()) {
            String featureId = this.getFeatureId(masterBillerResponse);
            String flowName = getFlowName(masterBillerResponse);
            commonAuthenResponse = new CommonAuthenticationValidationCommonPaymentResponse()
                    .setTotalPaymentAccumulateUsage(new BigDecimal(ocpDataResponse.getAmount()).add(customerCrmProfile.getPaymentAccuUsgAmt()))
                    .setFlowName(flowName)
                    .setFeatureId(featureId)
                    .setBillerCompCode(compCode);
        }

        BigDecimal amount = this.getAmount(request);

        BigDecimal totalAmount = amount.add(fee);
        if (WowPointUtils.isWowPointTransaction(request, cache.getCommonPaymentRule())) {
            totalAmount = totalAmount.subtract(request.getWowPoint().getDiscountAmount());
        }
        return new BillPayValidateDataAfterCallExternal()
                .setRequireCommonAuthen(commonAuthenResult.isRequireCommonAuthen())
                .setEasyPassAccountName(easyPassAccountName)
                .setCommonAuthentication(commonAuthenResponse)
                .setFeeAfterCalculated(fee)
                .setTotalAmount(totalAmount)
                .setCommonAuthenResult(commonAuthenResult);
    }

    private BigDecimal getAmount(ValidationCommonPaymentRequest request) {
        boolean isPayWithCreditCard = Optional.ofNullable(request.getCreditCard()).map(CreditCardValidationCommonPaymentRequest::isPayWithCreditCardFlag).orElse(false);
        return isPayWithCreditCard ? request.getCreditCard().getAmount() : request.getDeposit().getAmount();
    }

    private String getFlowName(MasterBillerResponse masterBillerResponse) {
        return masterBillerResponse.getBillerInfo().getBillerGroupType().equals(BILLER_GROUP_TOP_UP) ? COMMON_AUTH_TOP_UP_FLOW_NAME : COMMON_AUTH_BILL_FLOW_NAME;
    }

    private BigDecimal calculateFee(OCPBillPayment ocpBillPaymentResponse) {
        BigDecimal fee;
        boolean isWaiveFee = ocpBillPaymentResponse.getWaive() != null && ocpBillPaymentResponse.getWaive().getFlag().equals("Y");
        boolean isPayWithCreditCard = ocpBillPaymentResponse.getFromAccount().getAccountType().equals(ACCOUNT_TYPE_CCA);
        if (isWaiveFee || isPayWithCreditCard) {
            fee = new BigDecimal("0.00");
        } else if (ocpBillPaymentResponse.getFee().getBillPmtFee() != null) {
            fee = new BigDecimal(ocpBillPaymentResponse.getFee().getBillPmtFee());
        } else if (ocpBillPaymentResponse.getFee().getPaymentFee() != null) {
            fee = new BigDecimal(ocpBillPaymentResponse.getFee().getPaymentFee());
        } else {
            fee = new BigDecimal("0.00");
        }
        return fee;
    }

    private String getEasyPassAccountNameFromTransaction(List<AdditionalParam> additionalParams) {
        Optional<AdditionalParam> additionalParam = additionalParams.stream().filter(param -> param.getName().equals(BILL_ADDITIONAL_PARAM_KEY_NAME)).findFirst();
        if (additionalParam.isEmpty()) {
            return null;
        }

        String accountName = StringUtils.defaultIfBlank(CommonServiceUtils.getStringFromObject(additionalParam.get().getValue()), "");
        accountName = accountName.substring(0, accountName.indexOf('|'));
        accountName = accountName.replace("\n", "").replace("\r", "");
        return accountName;
    }

    private CommonAuthenResult validateIsRequireCommonAuthen(ValidationCommonPaymentRequest request, HttpHeaders headers, boolean isPayWithCreditCard, CustomerCrmProfile customerCrmProfile, MasterBillerResponse masterBiller) throws TMBCommonException {
        BigDecimal amount = isPayWithCreditCard ? request.getCreditCard().getAmount() : request.getDeposit().getAmount();

        if (StringUtils.equalsAnyIgnoreCase(BILLER_GROUP_TYPE_TOP_UP, masterBiller.getBillerInfo().getBillerGroupType())) {
            return dailyLimitPinFreeValidator.validateIsRequireCommonAuthForTopUp(headers, amount, false, customerCrmProfile);
        } else {
            return dailyLimitPinFreeValidator.validateIsRequireCommonAuthForBill(headers, amount, false, customerCrmProfile);
        }
    }

    @Override
    protected void saveFailedLog(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, ActivityBillPayValidationEvent activityEvent, BillPayPrepareDataValidate prepareData, Exception e) {
        MasterBillerResponse masterBiller = null;
        CreditCardSupplementary creditCardDetail = null;

        if (prepareData != null) {
            masterBiller = prepareData.getMasterBillerResponse();
            creditCardDetail = prepareData.getFromCreditCardDetail();
        }

        activityEvent = ActivityBillPayMapper.INSTANCE.updateActivityBillPayValidationFailed(activityEvent, masterBiller, cache, request, creditCardDetail, e);
        logEventPublisherService.saveActivityLog(activityEvent);
    }

    @Override
    protected TMBCommonException handleException(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, BillPayPrepareDataValidate prepareData, BillPayExternalValidateResponse externalResponse, BillPayValidateDataAfterCallExternal validateDataAfterCallExternal, Exception e) throws TMBCommonException, TMBCommonExceptionWithResponse {
        logger.error("ERROR process OnlineOfflineTopUpOCPBillPayValidation. : {}", e.getMessage(), e);
        if (e instanceof TMBCommonException ex) {
            throw ex;
        } else if (e instanceof TMBCommonExceptionWithResponse ex) {
            throw ex;
        } else {
            throw getUnhandledTmbCommonException(ResponseCode.FAILED_V2);
        }
    }

    @Override
    protected void updateCache(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, BillPayPrepareDataValidate prepareData, BillPayExternalValidateResponse externalResponse, BillPayValidateDataAfterCallExternal validateDataAfterCallExternal) throws JsonProcessingException {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final MasterBillerResponse masterBillerResponse = prepareData.getMasterBillerResponse();
        final var commonAuthentication = validateDataAfterCallExternal.getCommonAuthentication();
        final CreditCardSupplementary creditCardDetail = prepareData.getFromCreditCardDetail();
        final String compCode = cache.getPaymentInformation().getCompCode();

        WowPointRedeemConfirmRequest wowPointRedeemConfirmRequest = null;
        if (WowPointUtils.isWowPointTransaction(request, cache.getCommonPaymentRule())) {
            wowPointRedeemConfirmRequest = WowPointRedeemConfirmRequestMapper.INSTANCE.mapToWowPointRedeemConfirmRequestForOCPAndCustomBill(request, compCode, crmId);
        }

        OCPBillRequest ocpBillPaymentConfirmRequest = OCPBillRequestMapper.INSTANCE.toOCPBillRequestForConfirmOCPBillPayment(request, masterBillerResponse, cache, externalResponse, prepareData, wowPointRedeemConfirmRequest);

        var validateDraftCache = new ValidationCommonPaymentDraftCache()
                .setExternalConfirmRequest(new ExternalConfirmRequest()
                        .setOcpBillPaymentConfirmRequest(ocpBillPaymentConfirmRequest))
                .setFromDepositAccount(CacheMapper.INSTANCE.toDepositAccountInCache(prepareData.getFromDepositAccount()))
                .setRequireCommonAuthen(validateDataAfterCallExternal.isRequireCommonAuthen())
                .setFromCreditCardDetail(CacheMapper.INSTANCE.toCreditCardSupplementaryInCache(creditCardDetail))
                .setMasterBillerResponse(CacheMapper.INSTANCE.toMasterBillerResponseInCache(masterBillerResponse))
                .setCommonAuthentication(commonAuthentication)
                .setTotalAmount(validateDataAfterCallExternal.getTotalAmount())
                .setFeeCalculated(validateDataAfterCallExternal.getFeeAfterCalculated())
                .setWowPointRedeemConfirmRequest(wowPointRedeemConfirmRequest);

        cache.setCrmId(crmId);
        cache.setValidateRequest(request);
        cache.setValidateDraftCache(validateDraftCache);

        String cacheKey = COMMON_PAYMENT_CACHE_PREFIX + request.getTransactionId();
        cacheService.set(cacheKey, COMMON_PAYMENT_HASH_KEY_CACHE, cache);
    }
}
