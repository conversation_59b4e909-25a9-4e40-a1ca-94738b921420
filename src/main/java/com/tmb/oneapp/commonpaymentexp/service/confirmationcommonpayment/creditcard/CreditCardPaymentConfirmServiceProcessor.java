package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.creditcard;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityCreditCardBillPayConfirmationEvent;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityCustomSlipCompleteEvent;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.BaseConfirmDataAfterConfirmExternal;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.BasePrepareDataConfirm;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentResponseMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.CreditCardLogsConfirm;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.creditcard.CreditCardConfirmResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.creditcard.CreditCardConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.model.financiallog.FinancialCreditCardBillPayActivityLog;
import com.tmb.oneapp.commonpaymentexp.model.notification.NotificationCommonPayment;
import com.tmb.oneapp.commonpaymentexp.model.notification.NotificationMapper;
import com.tmb.oneapp.commonpaymentexp.model.transactionlog.TransactionActivityCreditCardBillPay;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.NotificationCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.client.CreditCardService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomerExpService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomersTransactionService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.BaseConfirmServiceHelper;
import com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.CommonValidateConfirmationService;
import com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.ConfirmationProcessingTemplate;
import com.tmb.oneapp.commonpaymentexp.service.dailylimit.DailyLimitService;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import static com.tmb.oneapp.commonpaymentexp.constant.ActivityConstant.ACTIVITY_LOG_COMMON_PAYMENT_CONFIRM_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.ActivityConstant.CUSTOM_SLIP_COMPLETE_BACKGROUND_TNX_TYPE_BILL;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.ACTIVITY_FAILURE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_ACCEPT_LANGUAGE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_APP_VERSION;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_CREDIT_CARD;

@Service
@RequiredArgsConstructor
public class CreditCardPaymentConfirmServiceProcessor extends ConfirmationProcessingTemplate<
        CreditCardConfirmResponse,
        BasePrepareDataConfirm,
        BaseConfirmDataAfterConfirmExternal,
        CreditCardLogsConfirm> {
    private static final TMBLogger<CreditCardPaymentConfirmServiceProcessor> logger = new TMBLogger<>(CreditCardPaymentConfirmServiceProcessor.class);
    private final PaymentService paymentService;
    private final CommonValidateConfirmationService commonValidateConfirmationService;
    private final BaseConfirmServiceHelper baseConfirmServiceHelper;
    private final LogEventPublisherService logEventPublisherService;
    private final AsyncHelper asyncHelper;
    private final CreditCardService creditCardService;
    private final CustomerExpService customerExpService;
    private final CustomersTransactionService customersTransactionService;
    private final NotificationCommonPaymentService notificationCommonPaymentService;
    private final DailyLimitService dailyLimitService;


    @Override
    public String getProcessorType() {
        return BILLER_PAYMENT_CREDIT_CARD;
    }

    @Override
    protected CreditCardLogsConfirm initialLogs(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache) {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String transactionTime = String.valueOf(System.currentTimeMillis());
        final String referenceId = draftCache.getValidateDraftCache().getExternalConfirmRequest().getCreditCardConfirmRequest().getBillPayment().getEpayCode();

        return new CreditCardLogsConfirm()
                .setActivityCreditCardBillPayConfirmationEvent(new ActivityCreditCardBillPayConfirmationEvent(ACTIVITY_LOG_COMMON_PAYMENT_CONFIRM_ID, headers, draftCache))
                .setFinancialCreditCardBillPayActivityLog(new FinancialCreditCardBillPayActivityLog(crmId, referenceId, draftCache, correlationId, transactionTime))
                .setTransactionActivityCreditCardBillPay(new TransactionActivityCreditCardBillPay(referenceId, crmId, draftCache, transactionTime))
                .setActivityCustomSlipCompleteEvent(new ActivityCustomSlipCompleteEvent(headers, request.getCustomSlip(), CUSTOM_SLIP_COMPLETE_BACKGROUND_TNX_TYPE_BILL));
    }

    @Override
    protected BasePrepareDataConfirm prepareData(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, CreditCardLogsConfirm logEvents) throws TMBCommonException {
        return baseConfirmServiceHelper.getBasePrepareDataConfirm(headers, logEvents.getFinancialCreditCardBillPayActivityLog().getTxnDt());
    }

    @Override
    protected BasePrepareDataConfirm validateData(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData) throws TMBCommonException {
        commonValidateConfirmationService.baseValidateData(request, headers, draftCache, prepareData);

        return prepareData;
    }

    @Override
    protected CreditCardConfirmResponse confirmWithExternalService(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData) throws TMBCommonException, TMBCommonExceptionWithResponse {
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String transactionId = request.getTransactionId();

        CreditCardConfirmRequest creditCardConfirmRequest = draftCache.getValidateDraftCache().getExternalConfirmRequest().getCreditCardConfirmRequest();
        return paymentService.confirmCreditCardPayment(correlationId, crmId, transactionId, creditCardConfirmRequest);
    }

    @Override
    protected BaseConfirmDataAfterConfirmExternal processAfterConfirmWithExternal(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, CreditCardConfirmResponse externalResponse) {
        final String transactionId = request.getTransactionId();
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final CustomerCrmProfile customerCrmProfile = prepareData.getCustomerCrmProfile();
        final boolean isPayByOwner = draftCache.getValidateDraftCache().getAdditionalParam().isPayByOwner();
        final boolean isRequireCommonAuth = draftCache.getValidateDraftCache().isRequireCommonAuthen();
        final String amountFromConfirmRequest = draftCache.getValidateDraftCache().getExternalConfirmRequest().getCreditCardConfirmRequest().getBillPayment().getAmount();
        final String transactionTime = prepareData.getTransactionTime();

        baseConfirmServiceHelper.baseClearDraftDataCache(transactionId);

        customersTransactionService.clearDepositCache(correlationId, crmId);

        this.clearCreditCardCache(headers);

        if (!isPayByOwner) {
            baseConfirmServiceHelper.baseUpdatePinFreeCountWithCondition(crmId, correlationId, customerCrmProfile, isRequireCommonAuth);
            dailyLimitService.updateAccumulateUsage(draftCache, customerCrmProfile, crmId, correlationId);
        }

        baseConfirmServiceHelper.baseExecuteCallbackIfConfiguredAsync(draftCache, transactionTime, amountFromConfirmRequest);

        NotificationCommonPayment notificationRequest = NotificationMapper.INSTANCE.toCreditCardNotification(draftCache, crmId, correlationId, transactionTime);
        asyncHelper.executeMethodAsyncSafelyVoid(() -> notificationCommonPaymentService.sendENotification(notificationRequest));

        return null;
    }

    private void clearCreditCardCache(HttpHeaders headers) {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String acceptLanguage = headers.getFirst(HEADER_ACCEPT_LANGUAGE);
        final String appVersion = headers.getFirst(HEADER_APP_VERSION);

        creditCardService.deleteCreditCardCache(correlationId, crmId);
        customerExpService.deleteCreditCardCache(correlationId, acceptLanguage, appVersion, crmId);
    }

    @Override
    protected void saveSuccessLog(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, CreditCardConfirmResponse externalResponse, CreditCardLogsConfirm logEvents, BaseConfirmDataAfterConfirmExternal processAfterConfirmResult) {
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String availableBalance = externalResponse.getBillPayment().getPayerAccount().getBalances().getAvailable();
        final ActivityCreditCardBillPayConfirmationEvent activityLog = logEvents.getActivityCreditCardBillPayConfirmationEvent();
        final ActivityCustomSlipCompleteEvent customSlipLog = logEvents.getActivityCustomSlipCompleteEvent();
        final TransactionActivityCreditCardBillPay transactionActivityLog = logEvents.getTransactionActivityCreditCardBillPay();

        FinancialCreditCardBillPayActivityLog financialLog = logEvents.getFinancialCreditCardBillPayActivityLog();
        financialLog.setTxnBal(availableBalance);

        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveActivityLog(activityLog, customSlipLog));
        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveFinancialLog(correlationId, financialLog));
        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveTransactionLog(correlationId, transactionActivityLog));
    }

    @Override
    protected ConfirmationCommonPaymentResponse mappingResponse(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, CreditCardConfirmResponse externalResponse) {
        return ConfirmationCommonPaymentResponseMapper.INSTANCE.mapToCreditCardConfirmationCommonPaymentResponse(draftCache, prepareData, externalResponse);
    }

    @Override
    protected TMBCommonException handleException(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, CreditCardLogsConfirm logEvents, Exception e) throws TMBCommonException, TMBCommonExceptionWithResponse {
        logger.error("ERROR process CreditCardConfirmation. : {}", e.getMessage());
        return baseConfirmServiceHelper.baseHandleException(request, e);
    }

    @Override
    protected void saveFailedLog(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, CreditCardLogsConfirm logEvents, Exception e) {
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);

        TransactionActivityCreditCardBillPay transactionActivityLog = logEvents.getTransactionActivityCreditCardBillPay();
        ActivityCreditCardBillPayConfirmationEvent activityLog = logEvents.getActivityCreditCardBillPayConfirmationEvent();
        FinancialCreditCardBillPayActivityLog financialLog = logEvents.getFinancialCreditCardBillPayActivityLog();

        transactionActivityLog.setTransactionStatus(ACTIVITY_FAILURE);
        activityLog.setFailureStatusWithReasonFromException(e);
        financialLog.setFailureStatusWithErrorCodeFromException(e);

        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveTransactionLog(correlationId, transactionActivityLog));
        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveActivityLog(activityLog));
        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveFinancialLog(correlationId, financialLog));
    }
}