package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.BaseConfirmDataAfterConfirmExternal;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.BasePrepareDataConfirm;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import org.jetbrains.annotations.Nullable;
import org.springframework.http.HttpHeaders;

public interface BaseConfirmServiceHelper {

    void baseClearDraftDataCache(String transactionId);

    void baseUpdatePinFreeCountWithCondition(String crmId, String correlationId, CustomerCrmProfile customerCrmProfile, boolean isRequireCommonAuth);

    void baseExecuteCallbackIfConfiguredAsync(CommonPaymentDraftCache draftCache, String transactionTime, String amount);

    TMBCommonException baseHandleException(ConfirmationCommonPaymentRequest request, Exception e) throws TMBCommonException, TMBCommonExceptionWithResponse;

    BasePrepareDataConfirm getBasePrepareDataConfirm(HttpHeaders headers, String transactionTimeFromFinLog) throws TMBCommonException;

    @Nullable
    BaseConfirmDataAfterConfirmExternal baseConfirmDataAfterConfirmExternal(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData);
}
