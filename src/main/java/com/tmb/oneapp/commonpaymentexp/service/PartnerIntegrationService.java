package com.tmb.oneapp.commonpaymentexp.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.nimbusds.jose.jwk.JWKSet;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitializationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.security.PublicKeyResponse;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.JoseUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@RequiredArgsConstructor
public class PartnerIntegrationService {

    private final JwkSetProvider jwkSetProvider;

    /**
     * Retrieves the public JWK set for a given partner and returns it as a JSON string.
     *
     * @param partnerName The identifier for the partner.
     * @return A {@link PublicKeyResponse} containing the JWK set as a JSON string.
     * @throws TMBCommonException if the JWKSet for the partner is not found or cannot be serialized.
     */
    public PublicKeyResponse getPublicKey(String partnerName) throws TMBCommonException {
        JWKSet jwkSet = jwkSetProvider.getJwkSet(partnerName);
        Map<String, Object> publicJwkSetMap = jwkSet.toJSONObject(true);
        return new PublicKeyResponse(publicJwkSetMap);
    }

    /**
     * Decrypts the JWE payload and deserializes it into an InitializationCommonPaymentRequest.
     *
     * @param encryptedPayload The JWE string.
     * @param partnerName      The identifier for the partner to find the correct decryption key.
     * @return The deserialized {@link InitializationCommonPaymentRequest}.
     * @throws TMBCommonException if decryption or deserialization fails.
     */
    public InitializationCommonPaymentRequest decryptInitialPayload(String encryptedPayload, String partnerName) throws TMBCommonException {
        JWKSet jwkSet = jwkSetProvider.getJwkSet(partnerName);
        String decryptedJson = JoseUtils.decrypt(encryptedPayload, jwkSet);
        try {
            return TMBUtils.convertStringToJavaObjWithTypeReference(decryptedJson, new TypeReference<>() {});
        } catch (JsonProcessingException e) {
            throw CommonServiceUtils.getUnhandledTmbCommonException(
                    ResponseCode.FAILED_V2,
                    "Failed to deserialize decrypted payload for partner: " + partnerName
            );
        }
    }
}
