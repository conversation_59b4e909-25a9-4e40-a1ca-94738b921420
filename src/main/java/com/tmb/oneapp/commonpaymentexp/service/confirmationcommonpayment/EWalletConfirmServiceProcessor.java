package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityCustomSlipCompleteEvent;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityEWalletConfirmationEvent;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.BaseConfirmDataAfterConfirmExternal;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.BasePrepareDataConfirm;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.EWalletLogsConfirm;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.Balance;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.EWalletETERequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.EWalletETEResponse;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.model.financiallog.FinancialEWallet;
import com.tmb.oneapp.commonpaymentexp.model.notification.NotificationCommonPayment;
import com.tmb.oneapp.commonpaymentexp.model.notification.NotificationMapper;
import com.tmb.oneapp.commonpaymentexp.model.transactionlog.TransactionActivityEWallet;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.NotificationCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomersTransactionService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.dailylimit.DailyLimitService;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import com.tmb.oneapp.commonpaymentexp.utils.DateUtils;
import com.tmb.oneapp.commonpaymentexp.utils.QRUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Optional;

import static com.tmb.oneapp.commonpaymentexp.constant.ActivityConstant.ACTIVITY_LOG_COMMON_PAYMENT_CONFIRM_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.ActivityConstant.CUSTOM_SLIP_COMPLETE_BACKGROUND_TNX_TYPE_TOP_UP;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.ACTIVITY_FAILURE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_TOPUP_E_WALLET;

@Service
@RequiredArgsConstructor
public class EWalletConfirmServiceProcessor extends ConfirmationProcessingTemplate<EWalletETEResponse,
        BasePrepareDataConfirm,
        BaseConfirmDataAfterConfirmExternal,
        EWalletLogsConfirm> {
    private static final TMBLogger<EWalletConfirmServiceProcessor> logger = new TMBLogger<>(EWalletConfirmServiceProcessor.class);
    private final AsyncHelper asyncHelper;
    private final DailyLimitService dailyLimitService;
    private final PaymentService paymentService;
    private final LogEventPublisherService logEventPublisherService;
    private final CustomersTransactionService customersTransactionService;
    private final NotificationCommonPaymentService notificationCommonPaymentService;
    private final CommonValidateConfirmationService commonValidateConfirmationService;
    private final BaseConfirmServiceHelper baseConfirmServiceHelper;


    @Override
    public String getProcessorType() {
        return BILLER_PAYMENT_TOPUP_E_WALLET;
    }

    @Override
    protected EWalletLogsConfirm initialLogs(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache) {
        final String transactionTime = String.valueOf(System.currentTimeMillis());
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String transactionRefNo = draftCache.getValidateDraftCache().getExternalConfirmRequest().getEWalletConfirmRequest().getTransactionReference();

        return new EWalletLogsConfirm()
                .setActivityEWalletConfirmationEvent(new ActivityEWalletConfirmationEvent(ACTIVITY_LOG_COMMON_PAYMENT_CONFIRM_ID, headers, draftCache))
                .setFinancialEWallet(new FinancialEWallet(crmId, correlationId, transactionTime, draftCache))
                .setTransactionActivityEWallet(new TransactionActivityEWallet(transactionRefNo, crmId, draftCache, transactionTime))
                .setActivityCustomSlipCompleteEvent(new ActivityCustomSlipCompleteEvent(headers, request.getCustomSlip(), CUSTOM_SLIP_COMPLETE_BACKGROUND_TNX_TYPE_TOP_UP));
    }

    @Override
    protected BasePrepareDataConfirm prepareData(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, EWalletLogsConfirm logEvents) throws TMBCommonException {
        final String transactionTimeFromFinLog = logEvents.getFinancialEWallet().getTxnDt();
        return baseConfirmServiceHelper.getBasePrepareDataConfirm(headers, transactionTimeFromFinLog);
    }

    @Override
    protected BasePrepareDataConfirm validateData(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData) throws TMBCommonException {
        final CustomerCrmProfile customerCrmProfile = prepareData.getCustomerCrmProfile();
        final String transactionId = request.getTransactionId();
        final BigDecimal amount = new BigDecimal(draftCache.getValidateDraftCache().getExternalConfirmRequest().getEWalletConfirmRequest().getAmount());

        commonValidateConfirmationService.verifyAuthentication(transactionId, draftCache, headers);
        commonValidateConfirmationService.validateTransactionByTransactionId(transactionId);
        dailyLimitService.validateDailyLimitExceeded(draftCache.getValidateDraftCache().getMasterBillerResponse().getBillerInfo().getBillerGroupType(), customerCrmProfile, amount);
        return prepareData;
    }

    @Override
    protected EWalletETEResponse confirmWithExternalService(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData) throws TMBCommonException, TMBCommonExceptionWithResponse {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String transactionId = request.getTransactionId();

        final EWalletETERequest eWalletConfirmRequest = draftCache.getValidateDraftCache().getExternalConfirmRequest().getEWalletConfirmRequest();
        return paymentService.confirmEWalletPayment(correlationId, crmId, transactionId, eWalletConfirmRequest);
    }

    @Override
    protected BaseConfirmDataAfterConfirmExternal processAfterConfirmWithExternal(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, EWalletETEResponse externalResponse) {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String transactionId = request.getTransactionId();
        final String transactionTime = prepareData.getTransactionTime();
        final CustomerCrmProfile customerCrmProfile = prepareData.getCustomerCrmProfile();
        final String amountFromConfirmRequest = draftCache.getValidateDraftCache().getExternalConfirmRequest().getEWalletConfirmRequest().getAmount();
        final boolean isRequireCommonAuth = draftCache.getValidateDraftCache().isRequireCommonAuthen();
        final String transactionRefNo = draftCache.getValidateDraftCache().getExternalConfirmRequest().getEWalletConfirmRequest().getTransactionReference();

        NotificationCommonPayment notificationRequest = NotificationMapper.INSTANCE.toCommonPaymentNotification(draftCache, transactionRefNo, crmId, correlationId, transactionTime);

        baseConfirmServiceHelper.baseClearDraftDataCache(transactionId);
        customersTransactionService.clearDepositCache(correlationId, crmId);
        dailyLimitService.updateAccumulateUsage(draftCache, customerCrmProfile, crmId, correlationId);
        baseConfirmServiceHelper.baseUpdatePinFreeCountWithCondition(crmId, correlationId, customerCrmProfile, isRequireCommonAuth);
        baseConfirmServiceHelper.baseExecuteCallbackIfConfiguredAsync(draftCache, transactionTime, amountFromConfirmRequest);
        asyncHelper.executeMethodAsyncSafelyVoid(() -> notificationCommonPaymentService.sendENotification(notificationRequest));

        return null;
    }

    @Override
    protected void saveSuccessLog(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, EWalletETEResponse externalResponse, EWalletLogsConfirm logEvents, BaseConfirmDataAfterConfirmExternal processAfterConfirmResult) {
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);

        final ActivityEWalletConfirmationEvent activityLog = logEvents.getActivityEWalletConfirmationEvent();
        final ActivityCustomSlipCompleteEvent customSlipLog = logEvents.getActivityCustomSlipCompleteEvent();
        final FinancialEWallet financialLog = logEvents.getFinancialEWallet();
        final TransactionActivityEWallet transactionActivityLog = logEvents.getTransactionActivityEWallet();

        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveActivityLog(activityLog, customSlipLog));
        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveFinancialLog(correlationId, financialLog));
        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveTransactionLog(correlationId, transactionActivityLog));

    }

    @Override
    protected ConfirmationCommonPaymentResponse mappingResponse(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, EWalletETEResponse externalResponse) {
        final String transactionRefNo = draftCache.getValidateDraftCache().getExternalConfirmRequest().getEWalletConfirmRequest().getTransactionReference();
        final String transactionTime = prepareData.getTransactionTime();
        final CustomerCrmProfile customerProfile = prepareData.getCustomerCrmProfile();

        ConfirmationCommonPaymentResponse response = new ConfirmationCommonPaymentResponse();
        response.setReferenceNo(transactionRefNo);
        response.setTransactionCreatedDatetime(DateUtils.formatTimestampToISO(transactionTime));
        response.setRemainingBalance(Optional.ofNullable(externalResponse).map(EWalletETEResponse::getBalance).map(Balance::getAvailable).orElse(null));
        response.setCompleteScreenDetail(draftCache.getPaymentInformation().getCompleteScreenDetail());
        response.setAutoSaveSlip(StringUtils.equalsAnyIgnoreCase("Y", customerProfile.getAutoSaveSlipMain()));

        response.setQr(QRUtils.generateMiniQRSafely(transactionRefNo));

        return response;
    }

    @Override
    protected TMBCommonException handleException(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, EWalletLogsConfirm logEvents, Exception e) throws TMBCommonException, TMBCommonExceptionWithResponse {
        logger.error("ERROR process EWalletConfirmation. : {}", e.getMessage());
        throw baseConfirmServiceHelper.baseHandleException(request, e);
    }

    @Override
    protected void saveFailedLog(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, EWalletLogsConfirm logEvents, Exception e) {
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);

        ActivityEWalletConfirmationEvent activityEWalletLog = logEvents.getActivityEWalletConfirmationEvent();
        FinancialEWallet financialEWalletLog = logEvents.getFinancialEWallet();
        TransactionActivityEWallet transactionEWalletLog = logEvents.getTransactionActivityEWallet();

        activityEWalletLog.setFailureStatusWithReasonFromException(e);
        financialEWalletLog.setFailureStatusWithErrorCodeFromException(e);
        transactionEWalletLog.setTransactionStatus(ACTIVITY_FAILURE);

        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveActivityLog(activityEWalletLog));
        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveFinancialLog(correlationId, financialEWalletLog));
        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveTransactionLog(correlationId, transactionEWalletLog));

    }
}

