package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityBillPromptPayConfirmationEvent;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityCustomSlipCompleteEvent;
import com.tmb.oneapp.commonpaymentexp.model.cache.MasterBillerResponseInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.BaseConfirmDataAfterConfirmExternal;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.BasePrepareDataConfirm;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.BillPromptPayLogsConfirm;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEConfirmResponse;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.model.financiallog.FinancialBillPromptPay;
import com.tmb.oneapp.commonpaymentexp.model.notification.NotificationCommonPayment;
import com.tmb.oneapp.commonpaymentexp.model.notification.NotificationMapper;
import com.tmb.oneapp.commonpaymentexp.model.transactionlog.TransactionActivityBillPromptPay;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.NotificationCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomersTransactionService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.dailylimit.DailyLimitService;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import com.tmb.oneapp.commonpaymentexp.utils.DateUtils;
import com.tmb.oneapp.commonpaymentexp.utils.NullSafeUtils;
import com.tmb.oneapp.commonpaymentexp.utils.QRUtils;
import com.tmb.oneapp.commonpaymentexp.validator.BaseBillPayValidator;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

import static com.tmb.oneapp.commonpaymentexp.constant.ActivityConstant.ACTIVITY_LOG_COMMON_PAYMENT_CONFIRM_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.ActivityConstant.CUSTOM_SLIP_COMPLETE_BACKGROUND_TNX_TYPE_TOP_UP;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.ACTIVITY_FAILURE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_BILL_PROMPT_PAY;

@Service
@RequiredArgsConstructor
public class BillPromptPayConfirmServiceProcessor extends ConfirmationProcessingTemplate<PromptPayETEConfirmResponse,
        BasePrepareDataConfirm,
        BaseConfirmDataAfterConfirmExternal,
        BillPromptPayLogsConfirm> {
    private static final TMBLogger<BillPromptPayConfirmServiceProcessor> logger = new TMBLogger<>(BillPromptPayConfirmServiceProcessor.class);
    private final AsyncHelper asyncHelper;
    private final DailyLimitService dailyLimitService;
    private final PaymentService paymentService;
    private final LogEventPublisherService logEventPublisherService;
    private final CustomersTransactionService customersTransactionService;
    private final NotificationCommonPaymentService notificationCommonPaymentService;
    private final CommonValidateConfirmationService commonValidateConfirmationService;
    private final BaseConfirmServiceHelper baseConfirmServiceHelper;
    private final BaseBillPayValidator baseBillPayValidator;

    @Value("${qr.payment.iso20022.flag:false}")
    private boolean isQRISO20022FlagOn;

    @Override
    public String getProcessorType() {
        return BILLER_PAYMENT_BILL_PROMPT_PAY;
    }

    @Override
    protected BillPromptPayLogsConfirm initialLogs(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache) {
        final String transactionTime = String.valueOf(System.currentTimeMillis());
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String transactionRefNo = draftCache.getValidateDraftCache().getExternalConfirmRequest().getPromptPayConfirmRequest().getTransactionReference();

        var activityBillPromptPayConfirmationEvent = new ActivityBillPromptPayConfirmationEvent(ACTIVITY_LOG_COMMON_PAYMENT_CONFIRM_ID, headers, draftCache);

        return new BillPromptPayLogsConfirm()
                .setActivityBillPromptPayConfirmationEvent(activityBillPromptPayConfirmationEvent)
                .setFinancialBillPromptPay(new FinancialBillPromptPay(crmId, correlationId, transactionTime, draftCache, activityBillPromptPayConfirmationEvent.getActivityTypeId()))
                .setTransactionActivityBillPromptPay(new TransactionActivityBillPromptPay(transactionRefNo, crmId, draftCache, transactionTime))
                .setActivityCustomSlipCompleteEvent(new ActivityCustomSlipCompleteEvent(headers, request.getCustomSlip(), CUSTOM_SLIP_COMPLETE_BACKGROUND_TNX_TYPE_TOP_UP));
    }

    @Override
    protected BasePrepareDataConfirm prepareData(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BillPromptPayLogsConfirm logEvents) throws TMBCommonException {
        final String transactionTimeFromFinLog = logEvents.getFinancialBillPromptPay().getTxnDt();
        return baseConfirmServiceHelper.getBasePrepareDataConfirm(headers, transactionTimeFromFinLog);
    }

    @Override
    protected BasePrepareDataConfirm validateData(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData) throws TMBCommonException {
        final CustomerCrmProfile customerCrmProfile = prepareData.getCustomerCrmProfile();
        final String transactionId = request.getTransactionId();
        final BigDecimal amountFromValidateRequest = draftCache.getValidateRequest().getDeposit().getAmount();

        final MasterBillerResponseInCache masterBiller = draftCache.getValidateDraftCache().getMasterBillerResponse();

        baseBillPayValidator.validateServiceHours(masterBiller);
        commonValidateConfirmationService.verifyAuthentication(transactionId, draftCache, headers);
        commonValidateConfirmationService.validateTransactionByTransactionId(transactionId);
        dailyLimitService.validateDailyLimitExceeded(draftCache.getValidateDraftCache().getMasterBillerResponse().getBillerInfo().getBillerGroupType(), customerCrmProfile, amountFromValidateRequest);

        return prepareData;
    }

    @Override
    protected PromptPayETEConfirmResponse confirmWithExternalService(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData) throws TMBCommonException, TMBCommonExceptionWithResponse {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String transactionId = request.getTransactionId();

        PromptPayETEConfirmRequest confirmRequest = draftCache.getValidateDraftCache().getExternalConfirmRequest().getPromptPayConfirmRequest();
        PromptPayETEConfirmResponse eteResponse;
        if (isQRISO20022FlagOn) {
            eteResponse = paymentService.confirmBillPromptPayISO20022Payment(correlationId, crmId, transactionId, confirmRequest);
        } else {
            eteResponse = paymentService.confirmBillPromptPayPayment(correlationId, crmId, transactionId, confirmRequest);
        }

        return eteResponse;
    }

    @Override
    protected BaseConfirmDataAfterConfirmExternal processAfterConfirmWithExternal(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, PromptPayETEConfirmResponse externalResponse) {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String transactionId = request.getTransactionId();
        final String transactionTime = prepareData.getTransactionTime();
        final CustomerCrmProfile customerCrmProfile = prepareData.getCustomerCrmProfile();
        final String amountFromValidateRequest = String.valueOf(draftCache.getValidateRequest().getDeposit().getAmount());
        final boolean isRequireCommonAuth = draftCache.getValidateDraftCache().isRequireCommonAuthen();
        final String transactionRefNo = draftCache.getValidateDraftCache().getExternalConfirmRequest().getPromptPayConfirmRequest().getTransactionReference();

        NotificationCommonPayment notificationRequest = NotificationMapper.INSTANCE.toCommonPaymentNotification(draftCache, transactionRefNo, crmId, correlationId, transactionTime);

        baseConfirmServiceHelper.baseClearDraftDataCache(transactionId);
        customersTransactionService.clearDepositCache(correlationId, crmId);
        dailyLimitService.updateAccumulateUsage(draftCache, customerCrmProfile, crmId, correlationId);
        baseConfirmServiceHelper.baseUpdatePinFreeCountWithCondition(crmId, correlationId, customerCrmProfile, isRequireCommonAuth);
        baseConfirmServiceHelper.baseExecuteCallbackIfConfiguredAsync(draftCache, transactionTime, amountFromValidateRequest);
        asyncHelper.executeMethodAsyncSafelyVoid(() -> notificationCommonPaymentService.sendENotification(notificationRequest));

        return null;
    }

    @Override
    protected void saveSuccessLog(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, PromptPayETEConfirmResponse externalResponse, BillPromptPayLogsConfirm logEvents, BaseConfirmDataAfterConfirmExternal processAfterConfirmResult) {
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String availableBalanceFromETEResponse = String.valueOf(externalResponse.getBalance().getAvailable());
        final var activityLog = logEvents.getActivityBillPromptPayConfirmationEvent();
        final var customSlipLog = logEvents.getActivityCustomSlipCompleteEvent();
        final var transactionActivityLog = logEvents.getTransactionActivityBillPromptPay();

        var financialLog = logEvents.getFinancialBillPromptPay();
        financialLog.setTxnBal(availableBalanceFromETEResponse);

        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveActivityLog(activityLog, customSlipLog));
        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveFinancialLog(correlationId, financialLog));
        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveTransactionLog(correlationId, transactionActivityLog));

    }

    @Override
    protected ConfirmationCommonPaymentResponse mappingResponse(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, PromptPayETEConfirmResponse externalResponse) {
        final String transactionRefNo = draftCache.getValidateDraftCache().getExternalConfirmRequest().getPromptPayConfirmRequest().getTransactionReference();
        final String transactionTime = prepareData.getTransactionTime();
        final CustomerCrmProfile customerProfile = prepareData.getCustomerCrmProfile();
        final BigDecimal availableBalanceFromETEResponse = NullSafeUtils.getSafeNull(() -> externalResponse.getBalance().getAvailable());

        ConfirmationCommonPaymentResponse response = new ConfirmationCommonPaymentResponse();
        response.setReferenceNo(transactionRefNo);
        response.setTransactionCreatedDatetime(DateUtils.formatTimestampToISO(transactionTime));
        response.setRemainingBalance(availableBalanceFromETEResponse);
        response.setCompleteScreenDetail(draftCache.getPaymentInformation().getCompleteScreenDetail());
        response.setAutoSaveSlip(StringUtils.equalsAnyIgnoreCase("Y", customerProfile.getAutoSaveSlipMain()));
        response.setQr(QRUtils.generateMiniQRSafely(transactionRefNo));

        return response;
    }

    @Override
    protected TMBCommonException handleException(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, BillPromptPayLogsConfirm logEvents, Exception e) throws TMBCommonException, TMBCommonExceptionWithResponse {
        logger.error("ERROR process BillPromptPayConfirmation. : {}", e.getMessage());
        throw baseConfirmServiceHelper.baseHandleException(request, e);
    }

    @Override
    protected void saveFailedLog(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, BillPromptPayLogsConfirm logEvents, Exception e) {
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);

        var activityLog = logEvents.getActivityBillPromptPayConfirmationEvent();
        var financialLog = logEvents.getFinancialBillPromptPay();
        var transactionLog = logEvents.getTransactionActivityBillPromptPay();
        activityLog.setFailureStatusWithReasonFromException(e);
        financialLog.setFailureStatusWithErrorCodeFromException(e);
        transactionLog.setTransactionStatus(ACTIVITY_FAILURE);

        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveActivityLog(activityLog));
        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveFinancialLog(correlationId, financialLog));
        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveTransactionLog(correlationId, transactionLog));

    }
}

