package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ExternalConfirmResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.LogsForConfirm;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.PrepareDataForConfirm;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ProcessAfterConfirmWithExternal;
import org.springframework.http.HttpHeaders;

public abstract class ConfirmationProcessingTemplate<
        E extends ExternalConfirmResponse,
        P extends PrepareDataForConfirm,
        R extends ProcessAfterConfirmWithExternal,
        A extends LogsForConfirm
        > implements ConfirmationCommonPaymentProcessor {
    private static final TMBLogger<?> logger = new TMBLogger<>(ConfirmationProcessingTemplate.class);

    /**
     * This template method will be used to confirm common payment.
     *
     * @param request    common payment request
     * @param headers    http headers
     * @param draftCache common payment draft draftCache
     * @return confirmation common payment response
     * @throws TMBCommonException if any error occur
     */
    public ConfirmationCommonPaymentResponse executeConfirm(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache) throws TMBCommonException, TMBCommonExceptionWithResponse {
        E externalResponse;
        P prepareData = null;
        R processAfterConfirmResult = null;

        A logEvents = initialLogs(request, headers, draftCache);
        try {
            prepareData = prepareData(request, headers, draftCache, logEvents);

            prepareData = validateData(request, headers, draftCache, prepareData);

            externalResponse = confirmWithExternalService(request, headers, draftCache, prepareData);
        } catch (Exception e) {
            saveFailedLog(request, headers, draftCache, prepareData, logEvents, e);

            throw handleException(request, headers, draftCache, prepareData, logEvents, e);
        }

        try {
            processAfterConfirmResult = processAfterConfirmWithExternal(request, headers, draftCache, prepareData, externalResponse);
        } catch (Exception eIgnore) {
            logger.error("Ignore error process after confirm with external service. : {}", eIgnore.getMessage(), eIgnore);
            //Ignore exception cause after call external service success should not be throw exception.
        }

        try {
            saveSuccessLog(request, headers, draftCache, prepareData, externalResponse, logEvents, processAfterConfirmResult);
        } catch (Exception eIgnore) {
            logger.error("Ignore error save success log. : {}", eIgnore.getMessage(), eIgnore);
            //Ignore exception cause after call external service success should not be throw exception.
        }

        return mappingResponse(request, headers, draftCache, prepareData, externalResponse);
    }

    /**
     * Create initial log for confirm common payment.
     *
     * @param request    common payment request
     * @param headers    http headers
     * @param draftCache common payment draft cache
     * @return log events
     */
    @LogAround
    protected abstract A initialLogs(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache);

    /**
     * Prepares data for confirmation with an external service.
     *
     * @param request    the common payment request containing necessary details
     * @param headers    the HTTP headers for the request
     * @param draftCache the common payment draft cache holding payment information
     * @param logEvents  the log events associated with the confirmation process
     * @return the prepared data for external service confirmation
     * @throws TMBCommonException if any error occurs during data preparation
     */
    @LogAround
    protected abstract P prepareData(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, A logEvents) throws TMBCommonException;

    /**
     * Validate data before confirming with an external service.
     * <p>
     * This method is intended to perform any necessary validation before confirming with an external service.
     * <p>
     * If the validation fails, a {@link TMBCommonException} should be thrown.
     *
     * @param request     the common payment request
     * @param headers     http headers
     * @param draftCache  common payment draft cache
     * @param prepareData the prepare data for call external service
     * @return the prepare data after validated
     * @throws TMBCommonException common exception
     */
    @LogAround
    protected abstract P validateData(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, P prepareData) throws TMBCommonException;

    /**
     * Confirms with an external service. eg ETE service.
     *
     * @param request     the common payment request
     * @param headers     the HTTP headers
     * @param draftCache  the common payment draft cache
     * @param prepareData the prepared data used for the external service call
     * @return the response received from the external service
     * @throws TMBCommonException if an error occurs during the external service call
     */
    @LogAround
    protected abstract E confirmWithExternalService(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, P prepareData) throws TMBCommonException, TMBCommonExceptionWithResponse;

    /**
     * Processes the result after confirming with an external service.
     *
     * @param request          the common payment request
     * @param headers          the HTTP headers
     * @param draftCache       the common payment draft cache
     * @param prepareData      the prepared data used for the external service call
     * @param externalResponse the response received from the external service
     * @return the result of processing after confirmation with the external service
     */
    @LogAround
    protected abstract R processAfterConfirmWithExternal(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, P prepareData, E externalResponse);

    /**
     * Save log when confirm with external service success.
     *
     * @param request                   common payment request
     * @param headers                   http headers
     * @param draftCache                common payment draft cache
     * @param prepareData               prepare data for call external service
     * @param externalResponse          external response
     * @param logEvents                 log events
     * @param processAfterConfirmResult result after confirm with external service
     */
    @LogAround
    protected abstract void saveSuccessLog(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, P prepareData, E externalResponse, A logEvents, R processAfterConfirmResult);

    /**
     * Map external response to common payment response.
     *
     * @param request          common payment request
     * @param headers          http headers
     * @param draftCache       common payment draft cache
     * @param prepareData      prepare data for call external service
     * @param externalResponse external response
     * @return common payment response
     */
    @LogAround
    protected abstract ConfirmationCommonPaymentResponse mappingResponse(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, P prepareData, E externalResponse);

    /**
     * Handle exception when confirm with external service failed.
     *
     * @param request     common payment request
     * @param headers     http headers
     * @param draftCache  common payment draft cache
     * @param prepareData prepare data
     * @param logEvents   log events
     * @param e           exception
     * @return TMBCommonException
     * @throws TMBCommonException common exception
     */
    @LogAround
    protected abstract TMBCommonException handleException(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, P prepareData, A logEvents, Exception e) throws TMBCommonException, TMBCommonExceptionWithResponse;

    /**
     * Save log when confirm with external service failed.
     *
     * @param request     common payment request
     * @param headers     http headers
     * @param draftCache  common payment draft cache
     * @param prepareData prepare data for call external service
     * @param logEvents   log events
     * @param e           exception when call external service failed
     */
    @LogAround
    protected abstract void saveFailedLog(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, P prepareData, A logEvents, Exception e);

}
