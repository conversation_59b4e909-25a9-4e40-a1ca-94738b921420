package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.util.Optional;

import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_CACHE_PREFIX;
import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_HASH_KEY_CACHE;

@Service
@RequiredArgsConstructor
public class ConfirmationCommonPaymentService {
    private static final TMBLogger<ConfirmationCommonPaymentService> logger = new TMBLogger<>(ConfirmationCommonPaymentService.class);
    private final CacheService cacheService;
    private final ConfirmationCommonPaymentProcessorSelector confirmationCommonPaymentProcessorSelector;

    public ConfirmationCommonPaymentResponse confirmCommonPayment(ConfirmationCommonPaymentRequest request, HttpHeaders headers) throws TMBCommonException, TMBCommonExceptionWithResponse {
        String transactionId = request.getTransactionId();

        CommonPaymentDraftCache draftCache = getDraftCache(transactionId);

        ConfirmationCommonPaymentProcessor processor = getPaymentProcessor(draftCache);

        return processor.executeConfirm(request, headers, draftCache);
    }


    private CommonPaymentDraftCache getDraftCache(String transactionId) throws TMBCommonException {
        String cacheKey = COMMON_PAYMENT_CACHE_PREFIX + transactionId;
        return (CommonPaymentDraftCache) Optional.ofNullable(cacheService.get(cacheKey, COMMON_PAYMENT_HASH_KEY_CACHE, CommonPaymentDraftCache.class)).orElseThrow(() -> {
            logger.error("Cache not found for transaction ID: {}", transactionId);
            return CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.TRANSACTION_NOT_FOUND_ERROR);
        });
    }

    private ConfirmationCommonPaymentProcessor getPaymentProcessor(CommonPaymentDraftCache cache) throws TMBCommonException {
        String processorType = cache.getProcessorType();

        confirmationCommonPaymentProcessorSelector.validateTransactionType(processorType);

        ConfirmationCommonPaymentProcessor processor = confirmationCommonPaymentProcessorSelector.getProcessor(processorType);

        if (processor == null) {
            logger.error("No payment processor found for transaction type: {}", processorType);
            throw CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.FAILED_V2, "Invalid transaction type");
        }

        return processor;
    }
}