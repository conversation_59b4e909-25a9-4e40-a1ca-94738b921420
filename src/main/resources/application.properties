#================= START Mandatory config =================
server.port=8080
spring.application.name=common-payment-exp
spring.application.description=
oneapp.ocp.domain=dev3-oneapp.svc

#Common utility
utility.common.service.endpoint=http://common-service.${oneapp.ocp.domain}

private.key.location=keys/rsa_private.key
public.key.location=keys/rsa_public.key
#================= END Mandatory config =================

#================= START Kafka config =================
kafka.prefix.topic=
spring.kafka.jaas.options.username=appusr
spring.kafka.jaas.options.password=
spring.kafka.producer.bootstrap-servers=stmoneamqd1.tmbbank.local:9092

#ActivityConfig
com.tmb.oneapp.common.payment.exp.activity=${kafka.prefix.topic}activity
com.tmb.oneapp.common.payment.exp.financial=${kafka.prefix.topic}financial_log
com.tmb.oneapp.common.payment.exp.transaction=${kafka.prefix.topic}transaction_log

#================= END Kafka config =================

#================= START Feign group =================
#Customer account biz
feign.customers.account.biz.service.name=customer-account-biz
feign.customers.account.biz.service.url=http://customer-account-biz.${oneapp.ocp.domain}

#Customer exp
feign.customer.exp.service.name=customer-exp
feign.customer.exp.service.url=http://customers-exp-service.${oneapp.ocp.domain}

#Customer service
feign.customers.service.name=customer-service
feign.customers.service.url=http://customers-service.${oneapp.ocp.domain}

#Payment service
feign.payment.service.name=payment-service
feign.payment.service.url=http://payment-service.${oneapp.ocp.domain}

#Retail service
feign.retail.lending.biz.name=retail-lending-biz
feign.retail.lending.biz.url=http://retail-lending-biz.${oneapp.ocp.domain}

#Common service
feign.common.service.name=common-service
feign.common.service.url=http://common-service.${oneapp.ocp.domain}

#Credit-card service
feign.credit.card.service.name=creditcard-service
feign.credit.card.service.url=http://creditcard-service.${oneapp.ocp.domain}

#Customer transaction service
feign.customers.transaction.service.name=customers-transaction-service
feign.customers.transaction.service.endpoint=http://customers-transaction-service.${oneapp.ocp.domain}

#oauth service
feign.oauth.service.name=oneapp-auth-service
feign.oauth.service.endpoint=http://oneapp-auth-service.${oneapp.ocp.domain}

#notification service
feign.notification.service.name=notification-service
feign.notification.service.endpoint=http://notification-service.${oneapp.ocp.domain}
notification-service.e-noti.default.channel.th=\u0E17\u0E35\u0E17\u0E35\u0E1A\u0E35 \u0E17\u0E31\u0E0A
notification-service.e-noti.default.channel.en=ttb touch

#HP Exp
feign.hp.exp.service.name=hp-exp-service
feign.hp.exp.service.url=http://hp-exp-service.${oneapp.ocp.domain}

#Transfer service
feign.transfer.service.name=transfer-service
feign.transfer.service.url=http://transfer-service.${oneapp.ocp.domain}

#Bank service
feign.bank.service.name=bank-service
feign.bank.service.url=http://bank-service.${oneapp.ocp.domain}

#Account service
feign.account.service.name=account-service
feign.account.service.url=http://accounts-service.${oneapp.ocp.domain}

#Loyalty Biz
feign.loyalty.biz.name=loyalty-biz
feign.loyalty.biz.url=http://loyalty-biz.${oneapp.ocp.domain}
#================= END Feign group =================

#================= START DB config =================
#Redis
spring.redis.mode=standalone
spring.redis.host=***********
spring.redis.port=6379
spring.redis.pool.min-idle=0
spring.redis.pool.max-idle=8
spring.redis.pool.max-total=8
spring.redis.read-from=replicaPreferred
spring.redis.adaptive-refresh-trigger-timeout=15
spring.redis.periodic-refresh=15
#================= END DB config =================

#================= START Feature config =================
#Prefix-deeplink on Initial common-payment
common.payment.initial.deeplink.url=oneappvit://linkaccess/commonpayment
common.payment.initial.cache.expire.second=600

#FR payment accumulate usage limit
fr.payment.accumulate.usage.limit=200000

#FR payment accumulate usage limit
amlo.amount.validate.for.get.tax.id=700000

#Bill Prompt pay transaction validate ISO
qr.payment.iso20022.flag=false
#================= END Feature config =================

#======= REDIS CONFIG ===================================
#Redis Library Feature Configuration
redis.cache.enabled=true
redis.cache.custom.ttl=true
redis.template.enabled=true

#======= END REDIS CONFIG ===================================

#================= START Other config =================
#Swagger
springdoc.api-docs.path=/v1/common-payment-exp/api-docs
springdoc.swagger-ui.enabled=true
springdoc.api-docs.enabled=true
springdoc.swagger-ui.use-root-path=false
springdoc.swagger-ui.operationsSorter=alpha
springdoc.swagger-ui.urls[0].name=common-payment-exp
springdoc.swagger-ui.urls[0].url=/v1/common-payment-exp/api-docs
swagger.host=https://apis-portal.oneapp.tmbbank.local, http://localhost:${server.port}

#Add Inbound/OutBound Log
app.api.logging.enable=true
app.api.logging.max-length=10000
app.api.logging.url-patterns=*
app.api.logging.feign.enable=true
app.api.logging.feign.exclude-url-patterns=/actuator/**
#================= END Other config =================

#================= START JWK Set config =================
# JWKSet for signing and encryption. In a real environment, this should be loaded from a secure vault, not stored in plaintext.
# This is a JSON object on a single line.
jwk.sets.client.shopee={"keys":[{"p":"yzP5HSi5RnbjHO98mjH8s3rrqY-Uwx_GQCIzcWkTbZae2IgyJYsx7lauQ5VtD24JHBOTgs0LoZk9CKGFgl7kE0Bytiu0Q3HNH9gKMLRKql79lI6Kdyczb7o7JuWlY-Pw0cNq3lmRiWFEhhPxj-ElENr6VcDG7wJ9IP_WFfn6FJ0","kty":"RSA","q":"-A_HaxRfH5XT7XkzODjwAVvD010g6FJ4nU1MjS0RI0WrMekpn54HO2D_rj6qWVfnOmpQ4kl4_12hcwAE3bZDM-bl2sZrqpqgHorCfBpClhN95x2IS-vQLwNJlVmDUrFw_meY8SuY-8ivOLnx5ZqH11Gu8v9hA7U6Z6YuhFu14WE","d":"B2UZsVhv4BOtnOCOQ9jns6mk9meBahiEhE2tNgSP6mSl2iYQaRIEpnUPREsVrzAU01CIBJIRsUcttt4oRdcmEEvnT6pSiwYa6k_QIOxoMmsYHuwaWn8J8S9LwDTF8hWsE7ZwuGQIXpn-vm-t9MSAb1kPREhoHdgPa7PfyBq9gzvkEiuBSe4N6iEOsNBh5GvYcEWMPuclBqyRVe_JTXoUj6a4auWHbsWN4K5NmGt9inAg0G4HGao8iG35FQn3H85340Bi2EpbW8srTx3hpV1Sg4u2fzZRg1BK0vmQt8XgjV5RDTPANFAJ97wUKBLWBP1L5hKQ5ava8KGBYGNAY1UAQ","e":"AQAB","use":"sig","kid":"signing-key-shopee","qi":"rNFMDpC_aDjV38voxzn_uGfs3wiT99R41PgFfjCWvAGhIqjW3TR6GJ-hJtMy3KDhK1zT06yjsYZ2bTyjzbFWV2B6dDJ4cbhOiyg0hQoUyYL6HCM59gzO7lDT7V6LSnowTkelisU9D7vH9jU5y9o2tbXM7HxGYMzd_KlmTXqWNC8","dp":"NXz5zv4gPvcec0hq8R_FV-08SUbdLruXcNxP1EOHdKpYJ8E6Cf3o58bzWiDkM1KQnU5d3ulgbWor0bDKtvWJbbPiPiek7Lfw-bfhfs6GdPBKKaeXtSOhe4B2_5clgPTIOD8LS3oiQBpVckGfrXLjUduCV75F3zY1f9E4JlLFzFU","alg":"RS256","dq":"8gRTB4HEnMhND--jrUK_xlOFZv4BUSjFTJNSEJrbkZIzSGq3lWdUnvYv_gxdnvVIP5HigCQ2zM7I6a4nETj1r_miYCQef0CE38d5oCLuvAUgXRBPHeNgJb4LWI8QtOkx2MOyT_m9NEnDypCAFQcDaWvkSGgoermh1eYolDH17YE","n":"xObfqj2zOchJ1aa9nXO_2d1lPXuun65QGky1OHzfcM3S8W4ZbUDxEnu92j8qZQCx3IA1OnZAI96cx9JslbZaFUigZ-a2gHvDlGRhJr83El7XtzeanoPtt70doBbUDxOASF_fJn3_uCIgRjtkSfZ7pYTruzgq4QpV87KZ_uG1O1m2xZSIkXQ1K5PxgrRtwPZOBQ2Bk92h_JIuJnzbC7Z52xFwQuFvCkjUAdTZiTqH0hzawcydeSEPKDz-nhQaQbaAcHOVbHFq_YMY2MuBlEVDuZHEFHeBFnd5WsCHa9DC7Ch8BsIiM-1TWzo2k7T_pTTN6vFQFoM76qzm8-kg5eDMfQ"},{"p":"6dHywvGnY4paRk_WsUc7Hg5UqucJbfiJuWskgKS0SP3jERzZROc7f5plDC6Hs4ywYm1Pn9XWKVOIlRHvu_hjFPsX82_mtYdyBoF7m_WuLMScawM2j1rX_zm7ytK_vGh4bL-C02gqQAR-HdFGxWo_djZNfup8acYpdEC9STMCOrk","kty":"RSA","q":"1BXfks_7pqtAEsXjTffXmJumUo91Oe-a8cOaMh5HNIj33aHvfu87ImzYdFuYvdJGaXhRVDAKe-kRfs0SeAnTzT7Wf2qYC3Pea3cJt_dsPYu5HAEUJ1wSjgcv9lyqLLFaBPgjbJtFnv0hZ9AY3yozG5lNbIHAOnqPKJPAMp_c0Rs","d":"U9t3mfckJIms_vgBt6Tzg76sBrb5SVX6nPh-fslZIY5wYjpQ2JPDLh28B6TRiFvL-OGbMMEDJ7KfT0_hL4QBBjPLdd-b_lrILXSaBx2rXdPBOSS8A45prYOkUug3I4ynIA_gBwR0wBcclsSDjvIKgOANrzd2g5OgPJ4jCJUEVyVr63VRwXuT-7KZbaUSYl_v-8vxg2cKukGD37pJVLUAeJH2ytqMVmHVMvngUZyVosVV9cUVTlcHsbEC6a84Ggijhp1IPW1yx9d1sBwuoGChUOxRtsqQVQ5nnM-PGxpe9jQy1HRxIUzh7zUoCjGMulIZgr5VX1ISoxTsO6JPnCMbEQ","e":"AQAB","use":"enc","kid":"encryption-key-shopee","qi":"XCYGzy_DtCBeoi0YhGXrWQOnzt3111uTp5Tdsxa1XB_mySZIN_PuZd0KX1tpTV6ENck8UnTr_qqqVEETsGKZtJYQnYsIjRPNeB5JJuWrsKv4_VdLc6Y0CqQUad76RuQlHJ5A_3c9JfcwnmvXeLu351S9Vbtd3Oc2_vnpxrHbskM","dp":"tKdEpD_nL242v3pyCzhYwaka24xz6aaMalkQs-ANdxPJ-2_JooXxn25fwiC1MhFyOinwyjKCLB3hOiPIkPehPhWedLSxkPGcNNCX-3C_aNFjdP7fibULi6NEp7la8Kva11LENqiVdC7Ebi9COccUTzY3e-aZx3OWlb5rQQILJ-k","alg":"RSA-OAEP-256","dq":"CLOxDbQM7kS-h0apUeymYvOX-nE6bRsgr2TIMeA-KbCpAuX_DqVANuRiaTvM45T6IJ-4Lo0eEQF6H0rIxDYbA6PdZzL3dBjX-m7S7kJvpYLTIIwGyKqldEBlPmWE8fqA6A1KXs1sKN7X_QtzycxQdoRuwdH9EikvMIoHa8xVYhs","n":"wbXXdlRHUTeR_mckemFIF-nWMtQHi9hw_5FemlCmRX5aSwtHoEAQL8uvKecf1fajBWITKzJEmf1Wk_Tsg2PUYhvTJrXokz_KXh66i401v0uGhLbbysjQzGhcrJo-ozrGW0npH_3wyql7UgExqxrt-H9lXsOWKoTgGtayfMGvNPZfgLJN4JSt0oOqIBSudK70hntr5vK2-ClqhhnViZH0mCgjo1_hQMlWMdujWMlVi9rNxAkOQevsxVs3vrl0lOdgHNReuEtEzkEJxl4-3KEDWExiL0W3R5D7yCw1EYF64J_zPBPqXKjUCzmIrKPXG_LoC5KVSISFVNSBqCnikSk6gw"}]}
jwk.sets.client.lazada={"keys":[{"p":"vLGZxyEOlcyO7jCZfY2F6epG3v5soWBV21vS2yrNNiXV19RVe1nE4NCxGCMF-woCjU_0i-2rElkqTduaJTnwIX_vKHbG2HLTJpP47PeRzFDzRSsUV0eGL9qj_1HpMgcC5hbuqevOuAwEY7R0JdFQcKgqDCpZoVB4S61xlc1S7Gc","kty":"RSA","q":"zT6TOojJduMvG5ACu0ssS0ijh5LaGOaYpX05RkZHfApQKVh3lbhXovPHUtezg2tOzttQI2fi4srC4P3vdaQwann0RbjobyFvaohG-6YA-lf8LUc5d45CllqykrhBGTYflsEKWOT4KffG6c3whIrjQgWzZcrFcQmAOGAd2IQ6_M8","d":"JuM_OluGsLTDp67Q05tOBPWgdXw8CGbz4ss3vBfzdviRq4mG9C3-mfAMpAxaghK3cHGLTC2z77nVo6S3uT9j7zP8cg5xlHPy_keOEMx7qYT5Aveu4f5zj0fwuFsMYmdN9gbsyxVSMaVlXwlN77dFLKHlNyH4HTNp5W9PsespexIUNVCrYeozNm8tXk7SrShTvTU-dj7gXnFWbM1Tc0cDDJWgDi_kanl5PW4omXzv6Q7rfKaCtyDlU8hfmcINahYQvkscJz-U-EyDx22aNvL9-XCzMYdKSg8DH6XTCnT-XA0FbG98-jUZaItjRYUiM9Ul1aa0_SBrFAJzKR3hUdfRIw","e":"AQAB","use":"sig","kid":"signing-key-lazada","qi":"XzZtySlSZ2Zb7jaIhlRgp3KWyIvqNFlgIocg23O76eM4Z9Gv0zaOujEjcOVdFyKdlLE-FLN3PSwaZ4ACc2kMWmZ2lPVLk2cveMf4ns35Xo_-zYE1IyU-itqq9E_qlYf5eu_9lNupbgkUP1zBd-jkQDLUppnRz6DLquQx4UIhF5A","dp":"qqTisRbQHeC0kewZo1XNeVGJbjkg_IAmpQ5RjisTEpj83wcDl14DklypC28BBZpz3GAmhDUXtKw2edrTeD8BTwpnumxQP4b4Nbc0MvlhPSkoF7Fx38yrKbPNBCWLgtacZIDwrXK3q_ISNikHsPuuh3NNM1tJW31ujOH6CsN47pk","alg":"RS256","dq":"hGQS0DUfz0JoRp00ReANqFoosfD2Ig3M9SK7aSs4PTjh0Gmyaa9JSGF7d2KP9vunECPDo7MFVUl5P2xNpFLOsEAOY1MNC3JDzjItp3HDO7i5f_NrjEUFirriaWXVCBAzfCj1JbY8pcWgV3xx4u07_oBy4fxUM7_vJ25IkjtpWV0","n":"l0hXrNQPt1rLUIX52h5Hrwe35YI3EJ3cXIe9MBFgpze521HcZCgCAT2i5TeG9HJjajZh9vFzmFwMRT0SIsz3haPsAK6glxJi6hpuX8n4S6-XNQ1knZpoYKOhg_tM6-3M-gyMmAXazLMV5X5Bfsar2bjlO4xoUsfj3q2hwfe1gzmEhOmpIfRkYJOw_AmI1JvC_l4TUNua3TdKUamBLaXagS8YDL6Eno8ebmYEFOhs5jD-QEEFRwlRfUZOVJEjIAJdZ94SsFSOFE8mfIuMkPqd8OvuY4puBNQDtYnpw1oqjYUaGWH3-9YFFuZq1CV9yDaMvTyHY5R205e4PLbVUhiLSQ"},{"p":"wb7pGVgIdFTLi4MDlNbBkUmE-bktFP2IEDXrm9JESI4rJ4voI3-d1al-9NRSmMKoRDjDjJVBWjpwUoU6Mao9iCRwbMG-m05sNkA5GVcI3Oql20b86OSwGmIjB5Oqr7kdaSc5GoxtcSfN_kBL0RG-1zyq3knfVRbHN6_qp3xX_UM","kty":"RSA","q":"-cb5pNo4ac_Wwczlt1EP4E4EguVH3zQKcQaP8WIhsiu5A7D2jnd0jSwaBnBRmBYs7wPqpzOUgFwMc_6rTwG6SnR7Acy2ue9kCvwjHCAumWFjEZOdX7SjD6sAqSfs0B1BshOAikYosTR7CYrtZdRgVLW3TbyZrfMWNLEBi-BeCd8","d":"Ul7RqcCz4tfH0M4tk8Qbr10jRmmQuLnoefn4wPu9TN3ig8eGwY-Girr-iQpKLmxHa4WOxFd5d29jS9bCxmFxMXkDdmLbsNVy-uspKAxXURPm0tii2OVryTgIVHl0UEnV7_Iu6QBCqMQ1Zi7EO0m-o8ZHbZvCLJNSHp27MKiehwmrCs_Fz6VFrXUAHWaDR0pWPzLYppW_I5hhgksRaq2ID8FD1JUow19NsNhTUMW_rT_teaAufwrmTtXbmi1YvaLASTKucP7wWYF3uI_maef_VPEzEopqtF5EqcVld3nw6KBLbRCrFFYfIyJp6doJNT-kfuwdJ4j75UUb7Mmi9sDiDQ","e":"AQAB","use":"enc","kid":"encryption-key-lazada","qi":"KdBeF2bsHGZ16yxUjo9CpFf6tDy4T_qXiR2CetSCqj5Pdy12dpbH5d0jFjjGHRwfaTNs9wWzxh4xFASUzVbSHiI--33qlohn_8UhPoibRTC4LhTdFDZydZIUo6Qtj9LTsOuIg_f-H0GgVGosYdEZOLecY_h8Q3HCHhfUyQB6yx8","dp":"krALZi_bvtAQh-E6ze7HAUwrjLLh6hmGcdeROhm5r0FMdRWdPXs6sfExeQZkPtpqcbSUgZTjtP-32RKbm8yr567HG-CYgpeeoHiBgqj9wd06k8msZFv45RDWOr9XpNG_7cRPnMUje1_Ku6xMes83sVhyefhuScsELCpZvFMNf2c","alg":"RSA-OAEP-256","dq":"owaatF12CqIMIqvwl0jCWv2w9hpeYbGeywykcMFDCv58oIslMfGH0gih5-fxaZm3tD1TL9R7rZfmzOLAvy16gNQr4Q3TieJU4tKUu-vJXnTw62z0f53v--753G5ho6gk0-zkrduC_b4qPB1AO4F1anS8cnGRwLZznOqW_svvtPM","n":"vQlHUWRojuQ-JbkBHryOEKnZABH-Na9rcFaoXMT0BRWkz1BjxFpBeqzmthzgcTnh7mJu31OTFtKHjf1aHNmMHWM4A5IdD2uU5ScKi03jF9j8SVMycG99LWvwFWMufyd5C8oYUom1w9Z-58U231L3Tx7EoH5craNrrCBlZG2KCq0ijn1wvx0ev35Jo-og2Jzzx5Qr0QwX5Llbp29CNIX9SJm7kwKMI7vTPe1JDeCtsipOkNz_io_16MrOfnRemzxWe6ZyU-DpD8CKw1qF9yTmEPIJ9ZuzwkrhnkjcAysRYa6c_DUYe6QtSmEL_drM8yDBj5T5gcyHfd_64LePByb4XQ"}]}
#================= END JWK Set config =================
